/**
 * @fileoverview Core interfaces merkezi export dosyası
 * @description Bu dosya tüm core interface'leri merkezi bir noktadan export eder.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece interface export işlemini gerçekleştirir.
 *
 * Barrel export pattern kullanılarak kod organizasyonu sağlanır.
 * Bu yaklaşım Open/Closed Principle'a uygun olarak yeni interface'ler
 * eklendiğinde mevcut import'ları bozmaz.
 */

/**
 * @description BaseModel Export
 * Temel veri modeli ve enum'lar
 */
export {
  // Base Model Class
  BaseModel,

  // Enums
  DataStatus,
  OperationType,
  SortOrder,
  ValidationSeverity,

  // Type Guards
  isBaseModel,
  isDataStatus,
  isOperationType,
} from './BaseModel';

export type {
  // Interfaces
  IValidatable,
  ISerializable,
  ICloneable,
  IComparable,
} from './BaseModel';

/**
 * @description Read Data Source Export
 * Veri okuma işlemleri arayüzleri
 */
export type {
  // Query Options
  QueryOptions,
  SearchOptions,
  CountOptions,
  ExistsOptions,

  // Main Interface
  IReadDataSource,
} from './IReadDataSource';

export {
  // Type Guards
  isQueryOptions,
  isSearchOptions,
} from './IReadDataSource';

/**
 * @description Write Data Source Export
 * Veri yazma işlemleri arayüzleri
 */
export type {
  // Options Interfaces
  CreateOptions,
  UpdateOptions,
  DeleteOptions,
  BatchCreateOptions,
  BatchUpdateOptions,
  BatchDeleteOptions,

  // Result Interface
  BatchResult,

  // Main Interface
  IWriteDataSource,
} from './IWriteDataSource';

/**
 * @description Data Source Export
 * Birleşik veri kaynağı arayüzleri
 */
export type {
  // Connection Options
  ConnectionOptions,
  DataSourceInfo,
  HealthCheckResult,

  // Main Interface
  IDataSource,
} from './IDataSource';

export {
  // Type Guards
  isConnectionOptions,
  isDataSourceInfo,
  isHealthCheckResult,
} from './IDataSource';

/**
 * @description Repository Export
 * Repository pattern arayüzleri
 */
export type {
  // Options and Stats
  RepositoryOptions,
  RepositoryStats,
  DomainEvent,

  // Main Interface
  IRepository,
} from './IRepository';

export {
  // Type Guards
  isRepositoryOptions,
  isDomainEvent,
} from './IRepository';

/**
 * @description Re-export Common Types
 * Sık kullanılan tiplerin yeniden export'u
 */
export type {
  ID,
  Timestamp,
  Result,
  Success,
  Failure,
  ValidationResult,
  ValidationError,
  PaginationParams,
  PaginatedResult,
  SortParams,
  SortDirection,
  FilterCondition,
  FilterOperator,
} from '../../types';

/**
 * @description Utility Functions
 * Interface'ler için yardımcı fonksiyonlar
 */

// Import types for utility functions
import type { BaseModel } from './BaseModel';
import type { QueryOptions, SearchOptions } from './IReadDataSource';
import type { CreateOptions, UpdateOptions, DeleteOptions } from './IWriteDataSource';
import type { RepositoryOptions } from './IRepository';

/**
 * @function createQueryOptions
 * @description QueryOptions oluşturmak için yardımcı fonksiyon
 * @template T - Model tipi
 * @param options - Kısmi seçenekler
 * @returns Tam QueryOptions
 */
export function createQueryOptions<T extends BaseModel>(
  options: Partial<QueryOptions<T>> = {}
): QueryOptions<T> {
  return {
    ...options,
    sort: options.sort || [],
    filters: options.filters || [],
  };
}

/**
 * @function createSearchOptions
 * @description SearchOptions oluşturmak için yardımcı fonksiyon
 * @template T - Model tipi
 * @param query - Arama sorgusu
 * @param options - Kısmi seçenekler
 * @returns Tam SearchOptions
 */
export function createSearchOptions<T extends BaseModel>(
  query: string,
  options: Partial<Omit<SearchOptions<T>, 'query'>> = {}
): SearchOptions<T> {
  return {
    ...options,
    query,
    fuzzy: options.fuzzy ?? false,
    caseSensitive: options.caseSensitive ?? false,
    sort: options.sort || [],
    filters: options.filters || [],
  };
}

/**
 * @function createCreateOptions
 * @description CreateOptions oluşturmak için yardımcı fonksiyon
 * @param options - Kısmi seçenekler
 * @returns Tam CreateOptions
 */
export function createCreateOptions(
  options: Partial<CreateOptions> = {}
): CreateOptions {
  return {
    validate: options.validate ?? true,
    skipDuplicateCheck: options.skipDuplicateCheck ?? false,
    generateId: options.generateId ?? true,
    setTimestamps: options.setTimestamps ?? true,
  };
}

/**
 * @function createUpdateOptions
 * @description UpdateOptions oluşturmak için yardımcı fonksiyon
 * @param options - Kısmi seçenekler
 * @returns Tam UpdateOptions
 */
export function createUpdateOptions(
  options: Partial<UpdateOptions> = {}
): UpdateOptions {
  return {
    validate: options.validate ?? true,
    partial: options.partial ?? true,
    optimisticLocking: options.optimisticLocking ?? true,
    updateTimestamp: options.updateTimestamp ?? true,
    incrementVersion: options.incrementVersion ?? true,
  };
}

/**
 * @function createDeleteOptions
 * @description DeleteOptions oluşturmak için yardımcı fonksiyon
 * @param options - Kısmi seçenekler
 * @returns Tam DeleteOptions
 */
export function createDeleteOptions(
  options: Partial<DeleteOptions> = {}
): DeleteOptions {
  return {
    soft: options.soft ?? true,
    cascade: options.cascade ?? false,
    force: options.force ?? false,
  };
}

/**
 * @function createRepositoryOptions
 * @description RepositoryOptions oluşturmak için yardımcı fonksiyon
 * @template T - Model tipi
 * @param options - Kısmi seçenekler
 * @returns Tam RepositoryOptions
 */
export function createRepositoryOptions<T extends BaseModel>(
  options: Partial<RepositoryOptions<T>> = {}
): RepositoryOptions<T> {
  return {
    caching: options.caching ?? true,
    cacheTtl: options.cacheTtl ?? 300, // 5 dakika
    validation: options.validation ?? true,
    auditLog: options.auditLog ?? true,
    softDelete: options.softDelete ?? true,
    optimisticLocking: options.optimisticLocking ?? true,
    defaultSort: options.defaultSort || [],
    defaultFilters: options.defaultFilters || [],
  };
}
