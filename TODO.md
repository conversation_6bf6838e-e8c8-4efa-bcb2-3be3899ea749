# Srv-Takip Projesi Kurulum TODO Listesi

Bu TODO listesi, srv-takip projesinin SOLID prensiplerine uygun, interface seviyesinde bağımlılık yönetimi ile minimal bağımlılık sağlayacak şekilde kurulumu için adım adım rehberdir.

## 📋 Kurulum Sırası ve Bağımlılık Yönetimi

### 1. Temel Altyapı (Core Infrastructure) - Öncelik: YÜKSEK

Bu adımlar önce tamamlanmalı çünkü diğer tüm bileşenler bunlara bağımlı.

#### 1.1 TypeScript Tip Tanımları

```
[✅] src/env.d.ts                       # TypeScript ortam tip tanımları (Vue, Quasar vb.)
[✅] src/types/global.d.ts              # Global utility tipler ve ortak veri yapıları
[✅] src/types/api.d.ts                 # API request/response tipleri
[✅] src/types/vue.d.ts                 # Vue 3 Composition API ve component tipleri
[✅] src/types/quasar.d.ts              # Quasar Framework özel tip tanımları
[✅] src/types/pinia.d.ts               # Pinia store tip tanımları
[✅] src/types/index.ts                 # Merkezi tip export dosyası (barrel export)
```

#### 1.2 Temel Arayüzler (Interfaces) - ✅ TAMAMLANDI

```
[✅] src/core/interfaces/BaseModel.ts           # Tüm veri modellerinin base sınıfı ve enum'ları
[✅] src/core/interfaces/IReadDataSource.ts     # Veri okuma işlemleri arayüzü (BaseModel'e bağımlı)
[✅] src/core/interfaces/IWriteDataSource.ts    # Veri yazma işlemleri arayüzü (BaseModel'e bağımlı)
[✅] src/core/interfaces/IDataSource.ts         # Read+Write birleştiren arayüz (yukarıdaki 2'ye bağımlı)
[✅] src/core/interfaces/IRepository.ts         # Repository pattern arayüzü (BaseModel'e bağımlı)
[✅] src/core/interfaces/index.ts               # Merkezi interface export dosyası (barrel export)
```

#### 1.3 Loglama Sistemi Arayüzleri - ✅ TAMAMLANDI

```
[✅] src/logging/interfaces/ILogger.ts          # Logger arayüzü (hiçbir bağımlılığı yok)
[✅] src/logging/interfaces/index.ts            # Barrel export dosyası
```

#### 1.4 Bildirim Sistemi Arayüzleri - ✅ TAMAMLANDI

```
[✅] src/notification/interfaces/INotifier.ts   # Bildirim arayüzü (hiçbir bağımlılığı yok)
[✅] src/notification/interfaces/index.ts        # Barrel export dosyası
```

### 2. Çekirdek Uygulamalar (Core Implementations) - Öncelik: YÜKSEK

#### 2.1 Loglama Uygulaması

```
[ ] src/logging/ConsoleLogger.ts               # ILogger arayüzünü uygular
[ ] src/logging/useLogger.ts                   # Logger composable (ILogger'a bağımlı)
```

#### 2.2 Bildirim Uygulaması

```
[ ] src/notification/QuasarNotifier.ts         # INotifier arayüzünü uygular (Quasar'a bağımlı)
[ ] src/notification/useNotifier.ts            # Notifier composable (INotifier'a bağımlı)
```

#### 2.3 Bağımlılık Enjeksiyonu

```
[ ] src/core/di.ts                             # DI container (arayüzlere bağımlı, uygulamalara değil)
```

#### 2.4 Veri Kaynağı Uygulamaları

```
[ ] src/core/datasources/RestApiDataSource.ts  # IDataSource uygulaması (axios'a bağımlı)
[ ] src/core/datasources/FirebaseDataSource.ts # IDataSource uygulaması (firebase'e bağımlı)
```

#### 2.5 Repository Uygulaması

```
[ ] src/core/repositories/GenericRepository.ts # IRepository uygulaması (IDataSource'a bağımlı)
```

#### 2.6 Factory Pattern

```
[ ] src/core/factory.ts                        # DataSource factory (IDataSource'a bağımlı)
```

### 3. Durum Yönetimi (State Management) - Öncelik: ORTA

#### 3.1 Store Yapılandırması

```
[ ] src/stores/index.ts                        # Pinia store yapılandırması
[ ] src/stores/theme-store.ts                  # Tema yönetimi store'u (hiçbir core bağımlılığı yok)
[ ] src/stores/language-store.ts               # Dil yönetimi store'u (hiçbir core bağımlılığı yok)
[ ] src/stores/layout-store.ts                 # Layout yönetimi store'u (hiçbir core bağımlılığı yok)
```

### 4. Çoklu Dil Desteği (i18n) - Öncelik: ORTA

#### 4.1 Dil Dosyaları

```
[ ] src/i18n/tr-TR/index.ts                    # Türkçe çeviriler
[ ] src/i18n/en-US/index.ts                    # İngilizce çeviriler
[ ] src/i18n/index.ts                          # i18n yapılandırması (dil dosyalarına bağımlı)
```

### 5. UI Bileşenleri (Components) - Öncelik: ORTA

#### 5.1 Tema Bileşenleri

```
[ ] src/components/theme/ThemeToggle.vue       # theme-store.ts'e bağımlı
```

#### 5.2 Dil Bileşenleri

```
[ ] src/components/language/LanguageSelector.vue # language-store.ts'e bağımlı
```

#### 5.3 Layout Bileşenleri

```
[ ] src/components/layout/AppHeader.vue        # theme ve language bileşenlerine bağımlı
[ ] src/components/layout/AppDrawer.vue        # layout-store.ts'e bağımlı
```

### 6. Layout ve Router - Öncelik: ORTA

#### 6.1 Ana Layout

```
[ ] src/layouts/MainLayout.vue                 # AppHeader ve AppDrawer'a bağımlı
```

#### 6.2 Router Yapılandırması

```
[ ] src/router/routes.ts                       # Route tanımları (layout'lara bağımlı)
[ ] src/router/index.ts                        # Router yapılandırması (routes'a bağımlı)
```

### 7. Sayfalar (Pages) - Öncelik: DÜŞÜK

#### 7.1 Temel Sayfalar

```
[ ] src/pages/ErrorNotFound.vue                # 404 sayfası (hiçbir bağımlılığı yok)
[ ] src/pages/TestPage.vue                     # Test sayfası (core servislere bağımlı olabilir)
[ ] src/pages/IndexPage.vue                    # Ana sayfa/Dashboard
```

### 8. Boot Dosyaları (Application Bootstrap) - Öncelik: YÜKSEK

#### 8.1 Temel Boot Dosyaları

```
[ ] src/boot/.gitkeep                          # Git klasör işaretleyicisi
[ ] src/boot/axios.ts                          # HTTP client yapılandırması
[ ] src/boot/logger.ts                         # Logger başlatma (ConsoleLogger'a bağımlı)
[ ] src/boot/notifier.ts                       # Notifier başlatma (QuasarNotifier'a bağımlı)
[ ] src/boot/data-source.ts                    # DataSource başlatma (factory'ye bağımlı)
[ ] src/boot/theme.ts                          # Tema başlatma (theme-store'a bağımlı)
[ ] src/boot/i18n.ts                           # i18n başlatma (i18n yapılandırmasına bağımlı)
[ ] src/boot/error-handler.ts                  # Hata yakalama (logger'a bağımlı)
[ ] src/boot/user-module.js                    # User modülü başlatma
```

### 9. Stil Dosyaları - Öncelik: DÜŞÜK

```
[ ] src/css/quasar.variables.scss              # Quasar değişkenleri
[ ] src/css/app.scss                           # Ana uygulama stilleri
```

### 10. Statik Varlıklar - Öncelik: DÜŞÜK

```
[ ] src/assets/quasar-logo-vertical.svg        # Logo dosyası
```

### 11. Ana Uygulama Bileşeni - Öncelik: SON

```
[ ] src/App.vue                                # Ana uygulama bileşeni (tüm sistem hazır olduktan sonra)
```

## 🔗 Bağımlılık Yönetimi Prensipleri

### Interface Segregation Principle (ISP)

- **IReadDataSource** ve **IWriteDataSource** ayrı tutuldu
- Sadece ihtiyaç duyulan metodları içeren küçük arayüzler
- **IDataSource** ikisini birleştiren kompozit arayüz

### Dependency Inversion Principle (DIP)

- Üst seviye modüller (Repository, Service) alt seviye modüllere (DataSource uygulamaları) bağımlı değil
- Tüm bağımlılıklar arayüzler üzerinden
- **di.ts** dosyasında merkezi bağımlılık yönetimi

### Single Responsibility Principle (SRP)

- Her dosya tek bir sorumluluğa sahip
- Logger, Notifier, DataSource ayrı ayrı sistemler
- Boot dosyaları her biri tek bir sistemi başlatır

## ⚠️ Önemli Notlar

### Kurulum Sırası Kritik Noktaları:

1. **Önce arayüzler** - Hiçbir somut uygulamaya bağımlı değiller
2. **Sonra temel uygulamalar** - Sadece arayüzlere bağımlı
3. **DI Container** - Arayüzleri uygulamalara bağlar
4. **Store'lar** - UI bağımsız, core sistemlere bağımlı değil
5. **UI Bileşenleri** - Store'lara bağımlı
6. **Boot dosyaları** - Herşey hazır olduktan sonra sistemi başlatır

### Bağımlılık Kuralları:

- ❌ **Concrete sınıflara direkt bağımlılık YASAK**
- ✅ **Sadece interface'lere bağımlılık**
- ✅ **DI Container üzerinden injection**
- ✅ **Composable fonksiyonlar aracılığıyla erişim**

### Test Edilebilirlik:

- Her arayüz kolayca mock'lanabilir
- Unit testlerde gerçek implementasyonlar yerine mock'lar kullanılabilir
- Integration testlerde farklı DataSource'lar test edilebilir

Bu yapı ile projeniz SOLID prensiplerine tam uyumlu, test edilebilir ve genişletilebilir olacak.
