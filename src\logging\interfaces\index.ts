/**
 * @fileoverview Logging interfaces merkezi export dosyası
 * @description Bu dosya tüm logging interface'lerini merkezi bir noktadan export eder.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece interface export işlemini gerçekleştirir.
 *
 * Barrel export pattern kullanılarak kod organizasyonu sağlanır.
 * Bu yaklaşım Open/Closed Principle'a uygun olarak yeni interface'ler
 * eklendiğinde mevcut import'ları bozmaz.
 */

/**
 * @description Logger Interfaces Export
 * Loglama sistemi arayüzleri ve enum'ları
 */
export {
  // Enums
  LogLevel,

  // Interfaces
  type LogContext,
  type LogEntry,
  type LoggerConfiguration,
  type ILogFormatter,
  type ILogWriter,
  type ILogger,

  // Type Guards
  isLogLevel,
  isLogEntry,
  isLogContext,

  // Utility Functions
  getLogLevelPriority,
  shouldLog,
  createLogEntry,
  getDefaultLoggerConfiguration,
} from './ILogger';

/**
 * @description Re-export Common Types
 * Sık kullanılan tiplerin yeniden export'u
 */
export type {
  Timestamp,
  Result,
  Success,
  Failure,
} from '../../types';

/**
 * @description Utility Functions
 * Logging interface'leri için yardımcı fonksiyonlar
 */

/**
 * @function createLogger
 * @description Logger factory fonksiyonu için tip tanımı
 * Dependency Inversion Principle'a uygun olarak
 * concrete implementasyonlara bağımlı değildir
 */
export type LoggerFactory = (config?: Partial<LoggerConfiguration>) => ILogger;

/**
 * @function createLogFormatter
 * @description Log formatter factory fonksiyonu için tip tanımı
 */
export type LogFormatterFactory = (config?: Partial<LoggerConfiguration>) => ILogFormatter;

/**
 * @function createLogWriter
 * @description Log writer factory fonksiyonu için tip tanımı
 */
export type LogWriterFactory = (config?: Partial<LoggerConfiguration>) => ILogWriter;

/**
 * @description Environment Configuration Types
 * .env dosyasından okunacak loglama konfigürasyonu tipleri
 */

/**
 * @interface LoggingEnvironmentConfig
 * @description .env dosyasından okunacak loglama ayarları
 * Single Responsibility Principle'a uygun olarak
 * sadece environment konfigürasyonu ile ilgili özellikleri içerir
 */
export interface LoggingEnvironmentConfig {
  /**
   * @property LOG_LEVEL
   * @description Minimum log seviyesi
   * @default 'info'
   */
  readonly LOG_LEVEL?: string;

  /**
   * @property LOG_ENABLE_CONSOLE
   * @description Konsola log yazılıp yazılmayacağı
   * @default 'true'
   */
  readonly LOG_ENABLE_CONSOLE?: string;

  /**
   * @property LOG_ENABLE_FILE
   * @description Dosyaya log yazılıp yazılmayacağı
   * @default 'false'
   */
  readonly LOG_ENABLE_FILE?: string;

  /**
   * @property LOG_ENABLE_REMOTE
   * @description Uzak sunucuya log gönderilip gönderilmeyeceği
   * @default 'false'
   */
  readonly LOG_ENABLE_REMOTE?: string;

  /**
   * @property LOG_DATE_FORMAT
   * @description Tarih formatı
   * @default 'YYYY-MM-DD HH:mm:ss'
   */
  readonly LOG_DATE_FORMAT?: string;

  /**
   * @property LOG_INCLUDE_STACK_TRACE
   * @description Stack trace dahil edilip edilmeyeceği
   * @default 'true'
   */
  readonly LOG_INCLUDE_STACK_TRACE?: string;

  /**
   * @property LOG_MAX_FILE_SIZE
   * @description Maksimum log dosyası boyutu (byte)
   * @default '10485760' (10MB)
   */
  readonly LOG_MAX_FILE_SIZE?: string;

  /**
   * @property LOG_ROTATION
   * @description Log dosyası rotasyonu aktif mi
   * @default 'true'
   */
  readonly LOG_ROTATION?: string;

  /**
   * @property LOG_FILE_PATH
   * @description Log dosyası yolu
   * @default './logs'
   */
  readonly LOG_FILE_PATH?: string;

  /**
   * @property LOG_REMOTE_URL
   * @description Uzak log sunucusu URL'i
   */
  readonly LOG_REMOTE_URL?: string;

  /**
   * @property LOG_REMOTE_API_KEY
   * @description Uzak log sunucusu API anahtarı
   */
  readonly LOG_REMOTE_API_KEY?: string;
}

/**
 * @function parseEnvironmentConfig
 * @description Environment değişkenlerini LoggerConfiguration'a çevirir
 * @param env - Environment değişkenleri
 * @returns Parsed LoggerConfiguration
 */
export function parseEnvironmentConfig(env: LoggingEnvironmentConfig): LoggerConfiguration {
  const minLevel = isLogLevel(env.LOG_LEVEL) ? env.LOG_LEVEL : LogLevel.INFO;
  
  return {
    minLevel,
    enableConsole: env.LOG_ENABLE_CONSOLE !== 'false',
    enableFile: env.LOG_ENABLE_FILE === 'true',
    enableRemote: env.LOG_ENABLE_REMOTE === 'true',
    dateFormat: env.LOG_DATE_FORMAT || 'YYYY-MM-DD HH:mm:ss',
    includeStackTrace: env.LOG_INCLUDE_STACK_TRACE !== 'false',
    maxLogSize: env.LOG_MAX_FILE_SIZE ? parseInt(env.LOG_MAX_FILE_SIZE, 10) : 10 * 1024 * 1024,
    logRotation: env.LOG_ROTATION !== 'false',
  };
}

/**
 * @description Logger Registry Types
 * Birden fazla logger'ı yönetmek için registry tipleri
 */

/**
 * @interface LoggerRegistry
 * @description Logger kayıt defteri arayüzü
 * Single Responsibility Principle'a uygun olarak
 * sadece logger kayıt işlemlerini tanımlar
 */
export interface LoggerRegistry {
  /**
   * @method register
   * @description Yeni bir logger kaydeder
   * @param name - Logger adı
   * @param logger - Logger instance'ı
   */
  register(name: string, logger: ILogger): void;

  /**
   * @method get
   * @description Kayıtlı logger'ı getirir
   * @param name - Logger adı
   * @returns Logger instance'ı veya undefined
   */
  get(name: string): ILogger | undefined;

  /**
   * @method getDefault
   * @description Varsayılan logger'ı getirir
   * @returns Varsayılan logger
   */
  getDefault(): ILogger;

  /**
   * @method setDefault
   * @description Varsayılan logger'ı ayarlar
   * @param logger - Varsayılan logger
   */
  setDefault(logger: ILogger): void;

  /**
   * @method list
   * @description Kayıtlı tüm logger isimlerini listeler
   * @returns Logger isimleri
   */
  list(): readonly string[];

  /**
   * @method remove
   * @description Kayıtlı logger'ı kaldırır
   * @param name - Logger adı
   * @returns Kaldırma işleminin başarılı olup olmadığı
   */
  remove(name: string): boolean;

  /**
   * @method clear
   * @description Tüm kayıtlı logger'ları temizler
   */
  clear(): void;
}
