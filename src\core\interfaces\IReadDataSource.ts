/**
 * @fileoverview Veri okuma işlemleri arayüzü
 * @description Bu dosya veri okuma işlemleri için arayüz tanımlarını içerir.
 * SOLID prensiplerinden Interface Segregation Principle'a uygun olarak
 * sadece okuma işlemlerini tanımlar.
 *
 * Single Responsibility: Sadece veri okuma işlemleri
 * Open/Closed: Yeni okuma metodları eklenebilir
 * Liskov Substitution: Tüm implementasyonlar birbirinin yerini alabilir
 * Interface Segregation: Sadece okuma işlemleri, yazma işlemleri ayrı
 * Dependency Inversion: Soyut arayüz, concrete implementasyonlara bağımlı değil
 */

import type {
  ID,
  Result,
  PaginationParams,
  PaginatedResult,
  SortParams,
  FilterCondition,
} from '../../types';

import type { BaseModel } from './BaseModel';

/**
 * @interface QueryOptions
 * @description Sorgu seçenekleri arayüzü
 * @template T - Sorgulanacak model tipi
 */
export interface QueryOptions<T extends BaseModel> {
  /**
   * @property pagination
   * @description Sayfalama parametreleri
   */
  readonly pagination?: PaginationParams;

  /**
   * @property sort
   * @description Sıralama parametreleri
   */
  readonly sort?: SortParams<T>[];

  /**
   * @property filters
   * @description Filtreleme koşulları
   */
  readonly filters?: FilterCondition<T>[];

  /**
   * @property includes
   * @description İlişkili verilerin dahil edilmesi
   */
  readonly includes?: (keyof T)[];

  /**
   * @property fields
   * @description Sadece belirtilen alanların getirilmesi
   */
  readonly fields?: (keyof T)[];
}

/**
 * @interface SearchOptions
 * @description Arama seçenekleri arayüzü
 * @template T - Aranacak model tipi
 */
export interface SearchOptions<T extends BaseModel> extends QueryOptions<T> {
  /**
   * @property query
   * @description Arama sorgusu
   */
  readonly query: string;

  /**
   * @property searchFields
   * @description Arama yapılacak alanlar
   */
  readonly searchFields?: (keyof T)[];

  /**
   * @property fuzzy
   * @description Bulanık arama aktif mi
   */
  readonly fuzzy?: boolean;

  /**
   * @property caseSensitive
   * @description Büyük/küçük harf duyarlılığı
   */
  readonly caseSensitive?: boolean;
}

/**
 * @interface CountOptions
 * @description Sayma seçenekleri arayüzü
 * @template T - Sayılacak model tipi
 */
export interface CountOptions<T extends BaseModel> {
  /**
   * @property filters
   * @description Filtreleme koşulları
   */
  readonly filters?: FilterCondition<T>[];

  /**
   * @property distinct
   * @description Benzersiz kayıt sayımı
   */
  readonly distinct?: boolean;

  /**
   * @property field
   * @description Sayım yapılacak alan (distinct için)
   */
  readonly field?: keyof T;
}

/**
 * @interface ExistsOptions
 * @description Varlık kontrolü seçenekleri arayüzü
 * @template T - Kontrol edilecek model tipi
 */
export interface ExistsOptions<T extends BaseModel> {
  /**
   * @property filters
   * @description Filtreleme koşulları
   */
  readonly filters?: FilterCondition<T>[];
}

/**
 * @interface IReadDataSource
 * @description Veri okuma işlemleri arayüzü
 * @template T - Veri modeli tipi (BaseModel'den türetilmiş olmalı)
 *
 * Bu arayüz Interface Segregation Principle'a uygun olarak
 * sadece okuma işlemlerini tanımlar. Yazma işlemleri IWriteDataSource'ta tanımlıdır.
 */
export interface IReadDataSource<T extends BaseModel> {
  /**
   * @method findById
   * @description ID'ye göre tek bir kayıt getirir
   * @param id - Kayıt ID'si
   * @param options - Sorgu seçenekleri
   * @returns Kayıt veya null
   */
  findById(id: ID, options?: Omit<QueryOptions<T>, 'pagination' | 'sort' | 'filters'>): Promise<Result<T | null, Error>>;

  /**
   * @method findOne
   * @description Koşullara göre tek bir kayıt getirir
   * @param options - Sorgu seçenekleri
   * @returns İlk bulunan kayıt veya null
   */
  findOne(options?: Omit<QueryOptions<T>, 'pagination'>): Promise<Result<T | null, Error>>;

  /**
   * @method findMany
   * @description Koşullara göre birden fazla kayıt getirir
   * @param options - Sorgu seçenekleri
   * @returns Kayıt listesi
   */
  findMany(options?: QueryOptions<T>): Promise<Result<readonly T[], Error>>;

  /**
   * @method findAll
   * @description Tüm kayıtları getirir (dikkatli kullanılmalı)
   * @param options - Sorgu seçenekleri
   * @returns Tüm kayıtlar
   */
  findAll(options?: Omit<QueryOptions<T>, 'pagination'>): Promise<Result<readonly T[], Error>>;

  /**
   * @method findPaginated
   * @description Sayfalanmış kayıtları getirir
   * @param options - Sorgu seçenekleri (pagination zorunlu)
   * @returns Sayfalanmış sonuç
   */
  findPaginated(options: QueryOptions<T> & { pagination: PaginationParams }): Promise<Result<PaginatedResult<T>, Error>>;

  /**
   * @method search
   * @description Metin tabanlı arama yapar
   * @param options - Arama seçenekleri
   * @returns Arama sonuçları
   */
  search(options: SearchOptions<T>): Promise<Result<readonly T[], Error>>;

  /**
   * @method searchPaginated
   * @description Sayfalanmış metin tabanlı arama yapar
   * @param options - Arama seçenekleri (pagination zorunlu)
   * @returns Sayfalanmış arama sonuçları
   */
  searchPaginated(options: SearchOptions<T> & { pagination: PaginationParams }): Promise<Result<PaginatedResult<T>, Error>>;

  /**
   * @method count
   * @description Koşullara göre kayıt sayısını döndürür
   * @param options - Sayma seçenekleri
   * @returns Kayıt sayısı
   */
  count(options?: CountOptions<T>): Promise<Result<number, Error>>;

  /**
   * @method exists
   * @description Koşullara göre kayıt varlığını kontrol eder
   * @param options - Varlık kontrolü seçenekleri
   * @returns Kayıt var mı
   */
  exists(options?: ExistsOptions<T>): Promise<Result<boolean, Error>>;

  /**
   * @method existsById
   * @description ID'ye göre kayıt varlığını kontrol eder
   * @param id - Kayıt ID'si
   * @returns Kayıt var mı
   */
  existsById(id: ID): Promise<Result<boolean, Error>>;

  /**
   * @method getDistinctValues
   * @description Belirtilen alan için benzersiz değerleri getirir
   * @param field - Alan adı
   * @param options - Sorgu seçenekleri
   * @returns Benzersiz değerler
   */
  getDistinctValues<K extends keyof T>(
    field: K,
    options?: Omit<QueryOptions<T>, 'pagination' | 'sort' | 'fields'>
  ): Promise<Result<readonly T[K][], Error>>;

  /**
   * @method aggregate
   * @description Toplama işlemleri yapar (sum, avg, min, max)
   * @param field - Toplama yapılacak alan
   * @param operation - Toplama işlemi
   * @param options - Sorgu seçenekleri
   * @returns Toplama sonucu
   */
  aggregate<K extends keyof T>(
    field: K,
    operation: 'sum' | 'avg' | 'min' | 'max' | 'count',
    options?: Omit<QueryOptions<T>, 'pagination' | 'sort' | 'fields'>
  ): Promise<Result<number, Error>>;

  /**
   * @method groupBy
   * @description Gruplama işlemi yapar
   * @param field - Gruplama yapılacak alan
   * @param options - Sorgu seçenekleri
   * @returns Gruplu sonuçlar
   */
  groupBy<K extends keyof T>(
    field: K,
    options?: Omit<QueryOptions<T>, 'pagination' | 'sort' | 'fields'>
  ): Promise<Result<Record<string, readonly T[]>, Error>>;
}

/**
 * @description Type Guards - IReadDataSource için tip koruma fonksiyonları
 */

/**
 * @function isQueryOptions
 * @description Bir değerin QueryOptions olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns QueryOptions olup olmadığı
 */
export function isQueryOptions<T extends BaseModel>(value: unknown): value is QueryOptions<T> {
  return (
    typeof value === 'object' &&
    value !== null &&
    (
      !('pagination' in value) ||
      typeof (value as QueryOptions<T>).pagination === 'object'
    ) &&
    (
      !('sort' in value) ||
      Array.isArray((value as QueryOptions<T>).sort)
    ) &&
    (
      !('filters' in value) ||
      Array.isArray((value as QueryOptions<T>).filters)
    )
  );
}

/**
 * @function isSearchOptions
 * @description Bir değerin SearchOptions olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns SearchOptions olup olmadığı
 */
export function isSearchOptions<T extends BaseModel>(value: unknown): value is SearchOptions<T> {
  return (
    isQueryOptions<T>(value) &&
    'query' in value &&
    typeof (value as SearchOptions<T>).query === 'string'
  );
}
