/**
 * @fileoverview Birleşik veri kaynağı arayüzü
 * @description Bu dosya okuma ve yazma işlemlerini birleştiren arayüz tanımlarını içerir.
 * SOLID prensiplerinden Open/Closed Principle'a uygun olarak
 * IReadDataSource ve IWriteDataSource arayüzlerini birleştirir.
 *
 * Single Responsibility: Veri kaynağı işlemlerini birleştirir
 * Open/Closed: Yeni işlemler eklenebilir, mevcut arayüzler değişmez
 * Liskov Substitution: Tüm implementasyonlar birbirinin yerini alabilir
 * Interface Segregation: Okuma ve yazma arayüzlerini kompozit olarak birleştirir
 * Dependency Inversion: Soyut arayüz, concrete implementasyonlara bağımlı değil
 */

import type { Result } from '../../types';
import type { BaseModel } from './BaseModel';
import type { IReadDataSource } from './IReadDataSource';
import type { IWriteDataSource } from './IWriteDataSource';

/**
 * @interface ConnectionOptions
 * @description Veri kaynağı bağlantı seçenekleri
 */
export interface ConnectionOptions {
  /**
   * @property host
   * @description Sunucu adresi
   */
  readonly host?: string;

  /**
   * @property port
   * @description Port numarası
   */
  readonly port?: number;

  /**
   * @property database
   * @description Veritabanı adı
   */
  readonly database?: string;

  /**
   * @property username
   * @description Kullanıcı adı
   */
  readonly username?: string;

  /**
   * @property password
   * @description Şifre
   */
  readonly password?: string;

  /**
   * @property ssl
   * @description SSL kullanılsın mı
   */
  readonly ssl?: boolean;

  /**
   * @property timeout
   * @description Bağlantı timeout'u (ms)
   */
  readonly timeout?: number;

  /**
   * @property retryAttempts
   * @description Yeniden deneme sayısı
   */
  readonly retryAttempts?: number;

  /**
   * @property retryDelay
   * @description Yeniden deneme gecikmesi (ms)
   */
  readonly retryDelay?: number;

  /**
   * @property poolSize
   * @description Bağlantı havuzu boyutu
   */
  readonly poolSize?: number;

  /**
   * @property maxIdleTime
   * @description Maksimum boşta kalma süresi (ms)
   */
  readonly maxIdleTime?: number;
}

/**
 * @interface DataSourceInfo
 * @description Veri kaynağı bilgileri
 */
export interface DataSourceInfo {
  /**
   * @property name
   * @description Veri kaynağı adı
   */
  readonly name: string;

  /**
   * @property type
   * @description Veri kaynağı tipi
   */
  readonly type: 'rest-api' | 'firebase' | 'mongodb' | 'postgresql' | 'mysql' | 'sqlite' | 'memory';

  /**
   * @property version
   * @description Veri kaynağı versiyonu
   */
  readonly version: string;

  /**
   * @property connected
   * @description Bağlantı durumu
   */
  readonly connected: boolean;

  /**
   * @property lastConnected
   * @description Son bağlantı zamanı
   */
  readonly lastConnected?: Date;

  /**
   * @property features
   * @description Desteklenen özellikler
   */
  readonly features: readonly string[];
}

/**
 * @interface HealthCheckResult
 * @description Sağlık kontrolü sonucu
 */
export interface HealthCheckResult {
  /**
   * @property healthy
   * @description Sağlıklı mı
   */
  readonly healthy: boolean;

  /**
   * @property responseTime
   * @description Yanıt süresi (ms)
   */
  readonly responseTime: number;

  /**
   * @property message
   * @description Durum mesajı
   */
  readonly message?: string;

  /**
   * @property details
   * @description Detay bilgileri
   */
  readonly details?: Record<string, unknown>;

  /**
   * @property timestamp
   * @description Kontrol zamanı
   */
  readonly timestamp: Date;
}

/**
 * @interface IDataSource
 * @description Birleşik veri kaynağı arayüzü
 * @template T - Veri modeli tipi (BaseModel'den türetilmiş olmalı)
 *
 * Bu arayüz Composition Pattern kullanarak IReadDataSource ve IWriteDataSource
 * arayüzlerini birleştirir. Open/Closed Principle'a uygun olarak
 * mevcut arayüzleri değiştirmeden yeni işlevsellik ekler.
 */
export interface IDataSource<T extends BaseModel> extends IReadDataSource<T>, IWriteDataSource<T> {
  /**
   * @method connect
   * @description Veri kaynağına bağlanır
   * @param options - Bağlantı seçenekleri
   * @returns Bağlantı başarılı mı
   */
  connect(options?: ConnectionOptions): Promise<Result<boolean, Error>>;

  /**
   * @method disconnect
   * @description Veri kaynağından bağlantıyı keser
   * @returns Bağlantı kesme başarılı mı
   */
  disconnect(): Promise<Result<boolean, Error>>;

  /**
   * @method isConnected
   * @description Bağlantı durumunu kontrol eder
   * @returns Bağlı mı
   */
  isConnected(): boolean;

  /**
   * @method getInfo
   * @description Veri kaynağı bilgilerini getirir
   * @returns Veri kaynağı bilgileri
   */
  getInfo(): DataSourceInfo;

  /**
   * @method healthCheck
   * @description Sağlık kontrolü yapar
   * @returns Sağlık durumu
   */
  healthCheck(): Promise<Result<HealthCheckResult, Error>>;

  /**
   * @method ping
   * @description Veri kaynağına ping atar
   * @returns Ping süresi (ms)
   */
  ping(): Promise<Result<number, Error>>;

  /**
   * @method reset
   * @description Veri kaynağını sıfırlar (dikkatli kullanılmalı)
   * @returns Sıfırlama başarılı mı
   */
  reset(): Promise<Result<boolean, Error>>;

  /**
   * @method backup
   * @description Veri kaynağının yedeğini alır
   * @param path - Yedek dosya yolu
   * @returns Yedekleme başarılı mı
   */
  backup(path: string): Promise<Result<boolean, Error>>;

  /**
   * @method restore
   * @description Veri kaynağını yedekten geri yükler
   * @param path - Yedek dosya yolu
   * @returns Geri yükleme başarılı mı
   */
  restoreFromBackup(path: string): Promise<Result<boolean, Error>>;

  /**
   * @method migrate
   * @description Veri tabanı migrasyonu yapar
   * @param version - Hedef versiyon
   * @returns Migrasyon başarılı mı
   */
  migrate(version?: string): Promise<Result<boolean, Error>>;

  /**
   * @method seed
   * @description Test verisi ekler
   * @param data - Test verisi
   * @returns Seed işlemi başarılı mı
   */
  seed(data: readonly Partial<T>[]): Promise<Result<boolean, Error>>;

  /**
   * @method truncate
   * @description Tüm verileri siler (dikkatli kullanılmalı)
   * @returns Silme başarılı mı
   */
  truncate(): Promise<Result<boolean, Error>>;

  /**
   * @method getStatistics
   * @description Veri kaynağı istatistiklerini getirir
   * @returns İstatistikler
   */
  getStatistics(): Promise<Result<Record<string, number>, Error>>;

  /**
   * @method optimize
   * @description Veri kaynağını optimize eder
   * @returns Optimizasyon başarılı mı
   */
  optimize(): Promise<Result<boolean, Error>>;

  /**
   * @method vacuum
   * @description Veri kaynağını temizler (SQLite için)
   * @returns Temizleme başarılı mı
   */
  vacuum(): Promise<Result<boolean, Error>>;

  /**
   * @method analyze
   * @description Veri kaynağını analiz eder
   * @returns Analiz başarılı mı
   */
  analyze(): Promise<Result<boolean, Error>>;
}

/**
 * @description Type Guards - IDataSource için tip koruma fonksiyonları
 */

/**
 * @function isConnectionOptions
 * @description Bir değerin ConnectionOptions olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns ConnectionOptions olup olmadığı
 */
export function isConnectionOptions(value: unknown): value is ConnectionOptions {
  return (
    typeof value === 'object' &&
    value !== null &&
    (
      !('host' in value) ||
      typeof (value as ConnectionOptions).host === 'string'
    ) &&
    (
      !('port' in value) ||
      typeof (value as ConnectionOptions).port === 'number'
    ) &&
    (
      !('timeout' in value) ||
      typeof (value as ConnectionOptions).timeout === 'number'
    )
  );
}

/**
 * @function isDataSourceInfo
 * @description Bir değerin DataSourceInfo olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns DataSourceInfo olup olmadığı
 */
export function isDataSourceInfo(value: unknown): value is DataSourceInfo {
  return (
    typeof value === 'object' &&
    value !== null &&
    'name' in value &&
    'type' in value &&
    'version' in value &&
    'connected' in value &&
    'features' in value &&
    typeof (value as DataSourceInfo).name === 'string' &&
    typeof (value as DataSourceInfo).type === 'string' &&
    typeof (value as DataSourceInfo).version === 'string' &&
    typeof (value as DataSourceInfo).connected === 'boolean' &&
    Array.isArray((value as DataSourceInfo).features)
  );
}

/**
 * @function isHealthCheckResult
 * @description Bir değerin HealthCheckResult olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns HealthCheckResult olup olmadığı
 */
export function isHealthCheckResult(value: unknown): value is HealthCheckResult {
  return (
    typeof value === 'object' &&
    value !== null &&
    'healthy' in value &&
    'responseTime' in value &&
    'timestamp' in value &&
    typeof (value as HealthCheckResult).healthy === 'boolean' &&
    typeof (value as HealthCheckResult).responseTime === 'number' &&
    (value as HealthCheckResult).timestamp instanceof Date
  );
}
