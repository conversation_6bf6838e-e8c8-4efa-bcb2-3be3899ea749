/**
 * @fileoverview Temel veri modeli ve enum tanımları
 * @description Bu dosya tüm veri modellerinin temel sınıfını ve ortak enum'ları içerir.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece temel veri modeli özelliklerini tanımlar.
 *
 * Bu sınıf Dependency Inversion Principle'a uygun olarak soyut bir yapıdır.
 * Tüm concrete model sınıfları bu base sınıftan türetilmelidir.
 */

import type {
  ID,
  Timestamp,
  BaseEntity,
  ValidationResult,
  ValidationError,
} from '../../types';

/**
 * @description Data Status Enum - Veri durumu enum'u
 * Open/Closed Principle'a uygun olarak yeni durumlar eklenebilir
 */
export enum DataStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DELETED = 'deleted',
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

/**
 * @description Operation Type Enum - İşlem tipi enum'u
 * CRUD işlemlerini ve batch işlemlerini tanımlar
 */
export enum OperationType {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  BATCH_CREATE = 'batch_create',
  BATCH_UPDATE = 'batch_update',
  BATCH_DELETE = 'batch_delete',
}

/**
 * @description Sort Order Enum - Sıralama düzeni enum'u
 */
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * @description Validation Severity Enum - Doğrulama önem derecesi
 */
export enum ValidationSeverity {
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
}

/**
 * @interface IValidatable
 * @description Doğrulanabilir varlıklar için arayüz
 * Interface Segregation Principle'a uygun olarak
 * sadece doğrulama ile ilgili metodları içerir
 */
export interface IValidatable {
  /**
   * @method validate
   * @description Varlığı doğrular
   * @returns Doğrulama sonucu
   */
  validate(): ValidationResult<this>;

  /**
   * @method isValid
   * @description Varlığın geçerli olup olmadığını kontrol eder
   * @returns Geçerlilik durumu
   */
  isValid(): boolean;
}

/**
 * @interface ISerializable
 * @description Serileştirilebilir varlıklar için arayüz
 * Interface Segregation Principle'a uygun olarak
 * sadece serileştirme ile ilgili metodları içerir
 */
export interface ISerializable<T = Record<string, unknown>> {
  /**
   * @method toJSON
   * @description Varlığı JSON formatına dönüştürür
   * @returns JSON temsili
   */
  toJSON(): T;

  /**
   * @method fromJSON
   * @description JSON'dan varlık oluşturur
   * @param json - JSON verisi
   * @returns Oluşturulan varlık
   */
  fromJSON(json: T): this;
}

/**
 * @interface ICloneable
 * @description Klonlanabilir varlıklar için arayüz
 * Interface Segregation Principle'a uygun olarak
 * sadece klonlama ile ilgili metodları içerir
 */
export interface ICloneable<T = unknown> {
  /**
   * @method clone
   * @description Varlığın bir kopyasını oluşturur
   * @returns Klonlanmış varlık
   */
  clone(): T;

  /**
   * @method deepClone
   * @description Varlığın derin bir kopyasını oluşturur
   * @returns Derin klonlanmış varlık
   */
  deepClone(): T;
}

/**
 * @interface IComparable
 * @description Karşılaştırılabilir varlıklar için arayüz
 * Interface Segregation Principle'a uygun olarak
 * sadece karşılaştırma ile ilgili metodları içerir
 */
export interface IComparable<T = unknown> {
  /**
   * @method equals
   * @description İki varlığın eşit olup olmadığını kontrol eder
   * @param other - Karşılaştırılacak varlık
   * @returns Eşitlik durumu
   */
  equals(other: T): boolean;

  /**
   * @method compareTo
   * @description İki varlığı karşılaştırır
   * @param other - Karşılaştırılacak varlık
   * @returns Karşılaştırma sonucu (-1, 0, 1)
   */
  compareTo(other: T): number;
}

/**
 * @abstract class BaseModel
 * @description Tüm veri modellerinin temel sınıfı
 *
 * Single Responsibility Principle: Sadece temel model özelliklerini tanımlar
 * Open/Closed Principle: Genişlemeye açık, değişime kapalı
 * Liskov Substitution Principle: Alt sınıflar bu sınıfın yerini alabilir
 * Interface Segregation Principle: Küçük, özel amaçlı arayüzleri implement eder
 * Dependency Inversion Principle: Soyut bir yapıdır, concrete implementasyonlara bağımlı değil
 */
export abstract class BaseModel
  implements
  BaseEntity,
  IValidatable,
  ISerializable,
  ICloneable,
  IComparable {
  /**
   * @property id
   * @description Benzersiz kimlik
   */
  public readonly id: ID;

  /**
   * @property createdAt
   * @description Oluşturulma zamanı
   */
  public readonly createdAt: Timestamp;

  /**
   * @property updatedAt
   * @description Güncellenme zamanı
   */
  public updatedAt: Timestamp;

  /**
   * @property status
   * @description Veri durumu
   */
  public status: DataStatus;

  /**
   * @property version
   * @description Optimistic locking için versiyon numarası
   */
  public version: number;

  /**
   * @constructor
   * @description BaseModel constructor
   * @param id - Benzersiz kimlik
   * @param createdAt - Oluşturulma zamanı
   * @param updatedAt - Güncellenme zamanı
   * @param status - Veri durumu
   * @param version - Versiyon numarası
   */
  protected constructor(
    id: ID,
    createdAt: Timestamp,
    updatedAt: Timestamp,
    status: DataStatus = DataStatus.ACTIVE,
    version: number = 1
  ) {
    this.id = id;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.status = status;
    this.version = version;
  }

  /**
   * @method validate
   * @description Temel doğrulama kuralları
   * Alt sınıflar bu metodu override ederek özel doğrulama kuralları ekleyebilir
   * @returns Doğrulama sonucu
   */
  public validate(): ValidationResult<this> {
    const errors: ValidationError[] = [];

    // ID doğrulaması
    if (!this.id || this.id.length === 0) {
      errors.push({
        field: 'id',
        message: 'ID alanı zorunludur',
        code: 'REQUIRED_FIELD',
      });
    }

    // Timestamp doğrulaması
    if (!this.createdAt || this.createdAt <= 0) {
      errors.push({
        field: 'createdAt',
        message: 'Oluşturulma zamanı geçerli olmalıdır',
        code: 'INVALID_TIMESTAMP',
      });
    }

    if (!this.updatedAt || this.updatedAt <= 0) {
      errors.push({
        field: 'updatedAt',
        message: 'Güncellenme zamanı geçerli olmalıdır',
        code: 'INVALID_TIMESTAMP',
      });
    }

    // CreatedAt <= UpdatedAt kontrolü
    if (this.createdAt > this.updatedAt) {
      errors.push({
        field: 'updatedAt',
        message: 'Güncellenme zamanı oluşturulma zamanından önce olamaz',
        code: 'INVALID_TIMESTAMP_ORDER',
      });
    }

    // Version doğrulaması
    if (this.version < 1) {
      errors.push({
        field: 'version',
        message: 'Versiyon numarası 1 veya daha büyük olmalıdır',
        code: 'INVALID_VERSION',
      });
    }

    // Status doğrulaması
    if (!Object.values(DataStatus).includes(this.status)) {
      errors.push({
        field: 'status',
        message: 'Geçersiz durum değeri',
        code: 'INVALID_STATUS',
      });
    }

    return errors.length === 0
      ? { success: true, data: this }
      : { success: false, error: errors };
  }

  /**
   * @method isValid
   * @description Varlığın geçerli olup olmadığını kontrol eder
   * @returns Geçerlilik durumu
   */
  public isValid(): boolean {
    const result = this.validate();
    return result.success;
  }

  /**
   * @method toJSON
   * @description Varlığı JSON formatına dönüştürür
   * Alt sınıflar bu metodu override ederek özel serileştirme kuralları ekleyebilir
   * @returns JSON temsili
   */
  public toJSON(): Record<string, unknown> {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      status: this.status,
      version: this.version,
    };
  }

  /**
   * @method fromJSON
   * @description JSON'dan varlık oluşturur
   * Bu metod abstract olarak tanımlanmıştır çünkü her alt sınıf
   * kendi özel deserializasyon mantığını implement etmelidir
   * @param json - JSON verisi
   * @returns Oluşturulan varlık
   */
  public abstract fromJSON(json: Record<string, unknown>): this;

  /**
   * @method clone
   * @description Varlığın sığ bir kopyasını oluşturur
   * @returns Klonlanmış varlık
   */
  public abstract clone(): this;

  /**
   * @method deepClone
   * @description Varlığın derin bir kopyasını oluşturur
   * @returns Derin klonlanmış varlık
   */
  public abstract deepClone(): this;

  /**
   * @method equals
   * @description İki varlığın eşit olup olmadığını kontrol eder
   * Temel karşılaştırma ID üzerinden yapılır
   * Alt sınıflar bu metodu override ederek özel karşılaştırma kuralları ekleyebilir
   * @param other - Karşılaştırılacak varlık
   * @returns Eşitlik durumu
   */
  public equals(other: this): boolean {
    if (!other) return false;
    if (this === other) return true;
    return this.id === other.id;
  }

  /**
   * @method compareTo
   * @description İki varlığı karşılaştırır
   * Temel karşılaştırma createdAt üzerinden yapılır
   * Alt sınıflar bu metodu override ederek özel karşılaştırma kuralları ekleyebilir
   * @param other - Karşılaştırılacak varlık
   * @returns Karşılaştırma sonucu (-1, 0, 1)
   */
  public compareTo(other: this): number {
    if (this.createdAt < other.createdAt) return -1;
    if (this.createdAt > other.createdAt) return 1;
    return 0;
  }

  /**
   * @method touch
   * @description Varlığın güncellenme zamanını şu anki zamana ayarlar
   * Version numarasını da artırır (optimistic locking)
   */
  public touch(): void {
    this.updatedAt = Date.now() as Timestamp;
    this.version += 1;
  }

  /**
   * @method markAsDeleted
   * @description Varlığı silinmiş olarak işaretler (soft delete)
   */
  public markAsDeleted(): void {
    this.status = DataStatus.DELETED;
    this.touch();
  }

  /**
   * @method isDeleted
   * @description Varlığın silinmiş olup olmadığını kontrol eder
   * @returns Silinmiş durumu
   */
  public isDeleted(): boolean {
    return this.status === DataStatus.DELETED;
  }

  /**
   * @method isActive
   * @description Varlığın aktif olup olmadığını kontrol eder
   * @returns Aktif durumu
   */
  public isActive(): boolean {
    return this.status === DataStatus.ACTIVE;
  }
}

/**
 * @description Type Guards - BaseModel için tip koruma fonksiyonları
 */

/**
 * @function isBaseModel
 * @description Bir değerin BaseModel instance'ı olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns BaseModel instance'ı olup olmadığı
 */
export function isBaseModel(value: unknown): value is BaseModel {
  return value instanceof BaseModel;
}

/**
 * @function isDataStatus
 * @description Bir değerin geçerli DataStatus olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns Geçerli DataStatus olup olmadığı
 */
export function isDataStatus(value: unknown): value is DataStatus {
  return typeof value === 'string' && Object.values(DataStatus).includes(value as DataStatus);
}

/**
 * @function isOperationType
 * @description Bir değerin geçerli OperationType olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns Geçerli OperationType olup olmadığı
 */
export function isOperationType(value: unknown): value is OperationType {
  return typeof value === 'string' && Object.values(OperationType).includes(value as OperationType);
}
