/**
 * @fileoverview Bildirim sistemi arayüzü ve ilgili tip tanımları
 * @description Bu dosya bildirim sistemi için gerekli tüm interface'leri ve enum'ları içerir.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece bildirim işlemleri ile ilgili arayüzleri tanımlar.
 *
 * Interface Segregation Principle'a uygun olarak küçük, özel amaçlı
 * arayüzler oluşturulmuştur. Dependency Inversion Principle'a uygun
 * olarak soyut arayüzler tanımlanmıştır.
 */

import type { Timestamp, Result } from '../../types';

/**
 * @enum NotificationType
 * @description Bildirim türleri enum'u
 * Open/Closed Principle'a uygun olarak yeni türler eklenebilir
 * 
 * Türler önem sırasına göre:
 * SUCCESS < INFO < WARNING < ERROR < LOADING
 */
export enum NotificationType {
  /**
   * @description Başarılı işlemler için kullanılır
   * Yeşil renk ve onay ikonu ile gösterilir
   */
  SUCCESS = 'success',

  /**
   * @description Bilgilendirme mesajları için kullanılır
   * Mavi renk ve bilgi ikonu ile gösterilir
   */
  INFO = 'info',

  /**
   * @description Uyarı mesajları için kullanılır
   * Sarı/turuncu renk ve uyarı ikonu ile gösterilir
   */
  WARNING = 'warning',

  /**
   * @description Hata mesajları için kullanılır
   * Kırmızı renk ve hata ikonu ile gösterilir
   */
  ERROR = 'error',

  /**
   * @description Yükleme durumu için kullanılır
   * Spinner veya progress bar ile gösterilir
   */
  LOADING = 'loading',
}

/**
 * @enum NotificationChannel
 * @description Bildirim kanalları enum'u
 * Open/Closed Principle'a uygun olarak yeni kanallar eklenebilir
 */
export enum NotificationChannel {
  /**
   * @description Kısa süreli toast bildirimleri
   * Ekranın köşesinde görünür ve otomatik kaybolur
   */
  TOAST = 'toast',

  /**
   * @description Modal dialog bildirimleri
   * Kullanıcı etkileşimi gerektirir
   */
  DIALOG = 'dialog',

  /**
   * @description Banner bildirimleri
   * Sayfanın üst kısmında görünür
   */
  BANNER = 'banner',

  /**
   * @description Push bildirimleri
   * Tarayıcı bildirimleri veya mobil push
   */
  PUSH = 'push',

  /**
   * @description Inline bildirimleri
   * Sayfa içeriğinin bir parçası olarak gösterilir
   */
  INLINE = 'inline',
}

/**
 * @enum NotificationPosition
 * @description Bildirim pozisyonları enum'u
 * Open/Closed Principle'a uygun olarak yeni pozisyonlar eklenebilir
 */
export enum NotificationPosition {
  /**
   * @description Ekranın üst kısmında ortalanmış
   */
  TOP = 'top',

  /**
   * @description Ekranın üst-sol köşesinde
   */
  TOP_LEFT = 'top-left',

  /**
   * @description Ekranın üst-sağ köşesinde
   */
  TOP_RIGHT = 'top-right',

  /**
   * @description Ekranın alt kısmında ortalanmış
   */
  BOTTOM = 'bottom',

  /**
   * @description Ekranın alt-sol köşesinde
   */
  BOTTOM_LEFT = 'bottom-left',

  /**
   * @description Ekranın alt-sağ köşesinde
   */
  BOTTOM_RIGHT = 'bottom-right',

  /**
   * @description Ekranın sol kenarında ortalanmış
   */
  LEFT = 'left',

  /**
   * @description Ekranın sağ kenarında ortalanmış
   */
  RIGHT = 'right',

  /**
   * @description Ekranın tam ortasında
   */
  CENTER = 'center',
}

/**
 * @enum NotificationDuration
 * @description Bildirim süreleri enum'u
 * Open/Closed Principle'a uygun olarak yeni süreler eklenebilir
 */
export enum NotificationDuration {
  /**
   * @description Kısa süre (2 saniye)
   */
  SHORT = 2000,

  /**
   * @description Orta süre (4 saniye)
   */
  MEDIUM = 4000,

  /**
   * @description Uzun süre (6 saniye)
   */
  LONG = 6000,

  /**
   * @description Çok uzun süre (10 saniye)
   */
  EXTRA_LONG = 10000,

  /**
   * @description Kalıcı (manuel kapatılana kadar)
   */
  PERSISTENT = -1,
}

/**
 * @interface NotificationAction
 * @description Bildirim aksiyonu arayüzü
 * Interface Segregation Principle'a uygun olarak
 * sadece aksiyon bilgilerini içerir
 */
export interface NotificationAction {
  /**
   * @property id
   * @description Aksiyon benzersiz kimliği
   */
  readonly id: string;

  /**
   * @property label
   * @description Aksiyon etiketi (buton metni)
   */
  readonly label: string;

  /**
   * @property icon
   * @description Aksiyon ikonu (opsiyonel)
   */
  readonly icon?: string;

  /**
   * @property color
   * @description Aksiyon rengi (opsiyonel)
   */
  readonly color?: string;

  /**
   * @property handler
   * @description Aksiyon işleyici fonksiyonu
   */
  readonly handler: () => void | Promise<void>;

  /**
   * @property closeOnClick
   * @description Tıklandığında bildirimi kapat
   */
  readonly closeOnClick?: boolean;

  /**
   * @property disabled
   * @description Aksiyon devre dışı mı
   */
  readonly disabled?: boolean;
}

/**
 * @interface NotificationContent
 * @description Bildirim içeriği arayüzü
 * Single Responsibility Principle'a uygun olarak
 * sadece içerik bilgilerini içerir
 */
export interface NotificationContent {
  /**
   * @property title
   * @description Bildirim başlığı (opsiyonel)
   */
  readonly title?: string;

  /**
   * @property message
   * @description Bildirim mesajı
   */
  readonly message: string;

  /**
   * @property html
   * @description HTML içerik (opsiyonel)
   * Güvenlik nedeniyle dikkatli kullanılmalı
   */
  readonly html?: string;

  /**
   * @property icon
   * @description Bildirim ikonu (opsiyonel)
   */
  readonly icon?: string;

  /**
   * @property avatar
   * @description Kullanıcı avatarı (opsiyonel)
   */
  readonly avatar?: string;

  /**
   * @property image
   * @description Bildirim resmi (opsiyonel)
   */
  readonly image?: string;

  /**
   * @property actions
   * @description Bildirim aksiyonları (opsiyonel)
   */
  readonly actions?: readonly NotificationAction[];
}

/**
 * @interface NotificationContext
 * @description Bildirim bağlam bilgileri
 * Interface Segregation Principle'a uygun olarak
 * sadece bağlam bilgilerini içerir
 */
export interface NotificationContext {
  /**
   * @property userId
   * @description İşlemi yapan kullanıcının ID'si (varsa)
   */
  readonly userId?: string;

  /**
   * @property sessionId
   * @description Oturum ID'si (varsa)
   */
  readonly sessionId?: string;

  /**
   * @property component
   * @description Bildirimi üreten bileşen adı
   */
  readonly component?: string;

  /**
   * @property method
   * @description Bildirimi üreten metod adı
   */
  readonly method?: string;

  /**
   * @property tags
   * @description Bildirimi kategorize etmek için etiketler
   */
  readonly tags?: readonly string[];

  /**
   * @property metadata
   * @description Ek meta veriler
   */
  readonly metadata?: Record<string, unknown>;
}

/**
 * @interface NotificationOptions
 * @description Bildirim seçenekleri arayüzü
 * Single Responsibility Principle'a uygun olarak
 * sadece bildirim seçeneklerini içerir
 */
export interface NotificationOptions {
  /**
   * @property type
   * @description Bildirim türü
   */
  readonly type: NotificationType;

  /**
   * @property channel
   * @description Bildirim kanalı
   */
  readonly channel: NotificationChannel;

  /**
   * @property position
   * @description Bildirim pozisyonu (opsiyonel)
   */
  readonly position?: NotificationPosition;

  /**
   * @property duration
   * @description Bildirim süresi (opsiyonel)
   */
  readonly duration?: NotificationDuration | number;

  /**
   * @property persistent
   * @description Kalıcı bildirim mi
   */
  readonly persistent?: boolean;

  /**
   * @property dismissible
   * @description Kapatılabilir mi
   */
  readonly dismissible?: boolean;

  /**
   * @property multiline
   * @description Çok satırlı mı
   */
  readonly multiline?: boolean;

  /**
   * @property progress
   * @description Progress bar göster
   */
  readonly progress?: boolean;

  /**
   * @property timeout
   * @description Otomatik kapatma süresi (ms)
   */
  readonly timeout?: number;

  /**
   * @property color
   * @description Özel renk
   */
  readonly color?: string;

  /**
   * @property textColor
   * @description Metin rengi
   */
  readonly textColor?: string;

  /**
   * @property classes
   * @description Ek CSS sınıfları
   */
  readonly classes?: string | readonly string[] | undefined;

  /**
   * @property style
   * @description Inline stil
   */
  readonly style?: Record<string, string> | undefined;

  /**
   * @property group
   * @description Bildirim grubu (aynı gruptakiler birleştirilebilir)
   */
  readonly group?: string;

  /**
   * @property context
   * @description Bağlam bilgileri (opsiyonel)
   */
  readonly context?: NotificationContext | undefined;
}

/**
 * @interface NotificationConfiguration
 * @description Bildirim sistemi konfigürasyonu
 * Single Responsibility Principle'a uygun olarak
 * sadece sistem konfigürasyonu ile ilgili özellikleri içerir
 */
export interface NotificationConfiguration {
  /**
   * @property defaultChannel
   * @description Varsayılan bildirim kanalı
   */
  readonly defaultChannel: NotificationChannel;

  /**
   * @property defaultPosition
   * @description Varsayılan bildirim pozisyonu
   */
  readonly defaultPosition: NotificationPosition;

  /**
   * @property defaultDuration
   * @description Varsayılan bildirim süresi
   */
  readonly defaultDuration: NotificationDuration;

  /**
   * @property maxNotifications
   * @description Maksimum eşzamanlı bildirim sayısı
   */
  readonly maxNotifications: number;

  /**
   * @property enableSound
   * @description Ses efekti aktif mi
   */
  readonly enableSound: boolean;

  /**
   * @property enableVibration
   * @description Titreşim aktif mi (mobil)
   */
  readonly enableVibration: boolean;

  /**
   * @property enableGrouping
   * @description Bildirim gruplama aktif mi
   */
  readonly enableGrouping: boolean;

  /**
   * @property enablePersistence
   * @description Bildirim kalıcılığı aktif mi
   */
  readonly enablePersistence: boolean;

  /**
   * @property enableRichContent
   * @description Zengin içerik desteği aktif mi
   */
  readonly enableRichContent: boolean;

  /**
   * @property enableActions
   * @description Aksiyon desteği aktif mi
   */
  readonly enableActions: boolean;

  /**
   * @property theme
   * @description Bildirim teması
   */
  readonly theme?: 'light' | 'dark' | 'auto';

  /**
   * @property animations
   * @description Animasyon ayarları
   */
  readonly animations?: {
    readonly enter?: string;
    readonly leave?: string;
    readonly duration?: number;
  };
}

/**
 * @interface NotificationResult
 * @description Bildirim işlemi sonucu
 * Single Responsibility Principle'a uygun olarak
 * sadece sonuç bilgilerini içerir
 */
export interface NotificationResult {
  /**
   * @property id
   * @description Bildirim benzersiz kimliği
   */
  readonly id: string;

  /**
   * @property timestamp
   * @description Bildirim oluşturulma zamanı
   */
  readonly timestamp: Timestamp;

  /**
   * @property type
   * @description Bildirim türü
   */
  readonly type: NotificationType;

  /**
   * @property channel
   * @description Kullanılan kanal
   */
  readonly channel: NotificationChannel;

  /**
   * @property dismissed
   * @description Kapatıldı mı
   */
  readonly dismissed: boolean;

  /**
   * @property dismissedAt
   * @description Kapatılma zamanı (varsa)
   */
  readonly dismissedAt?: Timestamp;

  /**
   * @property actionClicked
   * @description Tıklanan aksiyon ID'si (varsa)
   */
  readonly actionClicked?: string;

  /**
   * @property error
   * @description Hata bilgisi (varsa)
   */
  readonly error?: Error;
}

/**
 * @interface INotificationFormatter
 * @description Bildirim formatı arayüzü
 * Interface Segregation Principle'a uygun olarak
 * sadece formatlama işlemlerini içerir
 */
export interface INotificationFormatter {
  /**
   * @method formatContent
   * @description Bildirim içeriğini formatlar
   * @param content - Formatlanacak içerik
   * @param options - Bildirim seçenekleri
   * @returns Formatlanmış içerik
   */
  formatContent(
    content: NotificationContent,
    options: NotificationOptions
  ): string;

  /**
   * @method formatTitle
   * @description Bildirim başlığını formatlar
   * @param title - Formatlanacak başlık
   * @param type - Bildirim türü
   * @returns Formatlanmış başlık
   */
  formatTitle(title: string, type: NotificationType): string;

  /**
   * @method formatMessage
   * @description Bildirim mesajını formatlar
   * @param message - Formatlanacak mesaj
   * @param type - Bildirim türü
   * @returns Formatlanmış mesaj
   */
  formatMessage(message: string, type: NotificationType): string;

  /**
   * @method formatActions
   * @description Bildirim aksiyonlarını formatlar
   * @param actions - Formatlanacak aksiyonlar
   * @returns Formatlanmış aksiyonlar
   */
  formatActions(actions: readonly NotificationAction[]): string;
}

/**
 * @interface INotificationRenderer
 * @description Bildirim render arayüzü
 * Interface Segregation Principle'a uygun olarak
 * sadece render işlemlerini içerir
 */
export interface INotificationRenderer {
  /**
   * @method render
   * @description Bildirimi render eder
   * @param content - Bildirim içeriği
   * @param options - Bildirim seçenekleri
   * @returns Render işleminin sonucu
   */
  render(
    content: NotificationContent,
    options: NotificationOptions
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method dismiss
   * @description Bildirimi kapatır
   * @param id - Bildirim ID'si
   * @returns Kapatma işleminin sonucu
   */
  dismiss(id: string): Promise<Result<void, Error>>;

  /**
   * @method dismissAll
   * @description Tüm bildirimleri kapatır
   * @param group - Belirli bir grup (opsiyonel)
   * @returns Kapatma işleminin sonucu
   */
  dismissAll(group?: string): Promise<Result<void, Error>>;

  /**
   * @method update
   * @description Mevcut bildirimi günceller
   * @param id - Bildirim ID'si
   * @param content - Yeni içerik
   * @param options - Yeni seçenekler
   * @returns Güncelleme işleminin sonucu
   */
  update(
    id: string,
    content: Partial<NotificationContent>,
    options?: Partial<NotificationOptions>
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method getActive
   * @description Aktif bildirimleri getirir
   * @param group - Belirli bir grup (opsiyonel)
   * @returns Aktif bildirimler
   */
  getActive(group?: string): Promise<Result<readonly NotificationResult[], Error>>;
}

/**
 * @interface INotifier
 * @description Ana bildirim arayüzü
 * Single Responsibility Principle'a uygun olarak
 * sadece bildirim işlemlerini tanımlar
 *
 * Dependency Inversion Principle'a uygun olarak
 * soyut bir arayüzdür, concrete implementasyonlara bağımlı değildir
 */
export interface INotifier {
  /**
   * @method success
   * @description Başarı bildirimi gönderir
   * @param message - Bildirim mesajı
   * @param options - Bildirim seçenekleri (opsiyonel)
   * @returns Bildirim işleminin sonucu
   */
  success(
    message: string,
    options?: Partial<NotificationOptions>
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method info
   * @description Bilgi bildirimi gönderir
   * @param message - Bildirim mesajı
   * @param options - Bildirim seçenekleri (opsiyonel)
   * @returns Bildirim işleminin sonucu
   */
  info(
    message: string,
    options?: Partial<NotificationOptions>
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method warning
   * @description Uyarı bildirimi gönderir
   * @param message - Bildirim mesajı
   * @param options - Bildirim seçenekleri (opsiyonel)
   * @returns Bildirim işleminin sonucu
   */
  warning(
    message: string,
    options?: Partial<NotificationOptions>
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method error
   * @description Hata bildirimi gönderir
   * @param message - Bildirim mesajı
   * @param error - Hata nesnesi (opsiyonel)
   * @param options - Bildirim seçenekleri (opsiyonel)
   * @returns Bildirim işleminin sonucu
   */
  error(
    message: string,
    error?: Error,
    options?: Partial<NotificationOptions>
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method loading
   * @description Yükleme bildirimi gönderir
   * @param message - Bildirim mesajı
   * @param options - Bildirim seçenekleri (opsiyonel)
   * @returns Bildirim işleminin sonucu
   */
  loading(
    message: string,
    options?: Partial<NotificationOptions>
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method notify
   * @description Belirtilen türde bildirim gönderir
   * @param type - Bildirim türü
   * @param content - Bildirim içeriği
   * @param options - Bildirim seçenekleri (opsiyonel)
   * @returns Bildirim işleminin sonucu
   */
  notify(
    type: NotificationType,
    content: NotificationContent,
    options?: Partial<NotificationOptions>
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method dismiss
   * @description Bildirimi kapatır
   * @param id - Bildirim ID'si
   * @returns Kapatma işleminin sonucu
   */
  dismiss(id: string): Promise<Result<void, Error>>;

  /**
   * @method dismissAll
   * @description Tüm bildirimleri kapatır
   * @param group - Belirli bir grup (opsiyonel)
   * @returns Kapatma işleminin sonucu
   */
  dismissAll(group?: string): Promise<Result<void, Error>>;

  /**
   * @method update
   * @description Mevcut bildirimi günceller
   * @param id - Bildirim ID'si
   * @param content - Yeni içerik
   * @param options - Yeni seçenekler
   * @returns Güncelleme işleminin sonucu
   */
  update(
    id: string,
    content: Partial<NotificationContent>,
    options?: Partial<NotificationOptions>
  ): Promise<Result<NotificationResult, Error>>;

  /**
   * @method getConfiguration
   * @description Mevcut bildirim konfigürasyonunu döner
   * @returns Bildirim konfigürasyonu
   */
  getConfiguration(): NotificationConfiguration;

  /**
   * @method updateConfiguration
   * @description Bildirim konfigürasyonunu günceller
   * @param config - Yeni konfigürasyon
   * @returns Güncelleme işleminin sonucu
   */
  updateConfiguration(
    config: Partial<NotificationConfiguration>
  ): Promise<Result<void, Error>>;

  /**
   * @method getActive
   * @description Aktif bildirimleri getirir
   * @param group - Belirli bir grup (opsiyonel)
   * @returns Aktif bildirimler
   */
  getActive(group?: string): Promise<Result<readonly NotificationResult[], Error>>;

  /**
   * @method clear
   * @description Tüm bildirimleri temizler ve kaynakları serbest bırakır
   * @returns Temizleme işleminin sonucu
   */
  clear(): Promise<Result<void, Error>>;
}

/**
 * @description Type Guards - Tip koruma fonksiyonları
 * Bu fonksiyonlar runtime'da tip güvenliği sağlar
 */

/**
 * @function isNotificationType
 * @description Bir değerin NotificationType enum'u olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns NotificationType olup olmadığı
 */
export function isNotificationType(value: unknown): value is NotificationType {
  return typeof value === 'string' && Object.values(NotificationType).includes(value as NotificationType);
}

/**
 * @function isNotificationChannel
 * @description Bir değerin NotificationChannel enum'u olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns NotificationChannel olup olmadığı
 */
export function isNotificationChannel(value: unknown): value is NotificationChannel {
  return typeof value === 'string' && Object.values(NotificationChannel).includes(value as NotificationChannel);
}

/**
 * @function isNotificationPosition
 * @description Bir değerin NotificationPosition enum'u olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns NotificationPosition olup olmadığı
 */
export function isNotificationPosition(value: unknown): value is NotificationPosition {
  return typeof value === 'string' && Object.values(NotificationPosition).includes(value as NotificationPosition);
}

/**
 * @function isNotificationDuration
 * @description Bir değerin NotificationDuration enum'u olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns NotificationDuration olup olmadığı
 */
export function isNotificationDuration(value: unknown): value is NotificationDuration {
  return typeof value === 'number' && Object.values(NotificationDuration).includes(value as NotificationDuration);
}

/**
 * @function isNotificationAction
 * @description Bir nesnenin NotificationAction interface'ini implement edip etmediğini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns NotificationAction olup olmadığı
 */
export function isNotificationAction(value: unknown): value is NotificationAction {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  const action = value as Record<string, unknown>;

  return (
    typeof action.id === 'string' &&
    typeof action.label === 'string' &&
    typeof action.handler === 'function' &&
    (action.icon === undefined || typeof action.icon === 'string') &&
    (action.color === undefined || typeof action.color === 'string') &&
    (action.closeOnClick === undefined || typeof action.closeOnClick === 'boolean') &&
    (action.disabled === undefined || typeof action.disabled === 'boolean')
  );
}

/**
 * @function isNotificationContent
 * @description Bir nesnenin NotificationContent interface'ini implement edip etmediğini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns NotificationContent olup olmadığı
 */
export function isNotificationContent(value: unknown): value is NotificationContent {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  const content = value as Record<string, unknown>;

  return (
    typeof content.message === 'string' &&
    (content.title === undefined || typeof content.title === 'string') &&
    (content.html === undefined || typeof content.html === 'string') &&
    (content.icon === undefined || typeof content.icon === 'string') &&
    (content.avatar === undefined || typeof content.avatar === 'string') &&
    (content.image === undefined || typeof content.image === 'string') &&
    (content.actions === undefined || (Array.isArray(content.actions) && content.actions.every(isNotificationAction)))
  );
}

/**
 * @function isNotificationOptions
 * @description Bir nesnenin NotificationOptions interface'ini implement edip etmediğini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns NotificationOptions olup olmadığı
 */
export function isNotificationOptions(value: unknown): value is NotificationOptions {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  const options = value as Record<string, unknown>;

  return (
    isNotificationType(options.type) &&
    isNotificationChannel(options.channel) &&
    (options.position === undefined || isNotificationPosition(options.position)) &&
    (options.duration === undefined || typeof options.duration === 'number' || isNotificationDuration(options.duration)) &&
    (options.persistent === undefined || typeof options.persistent === 'boolean') &&
    (options.dismissible === undefined || typeof options.dismissible === 'boolean')
  );
}

/**
 * @function isNotificationResult
 * @description Bir nesnenin NotificationResult interface'ini implement edip etmediğini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns NotificationResult olup olmadığı
 */
export function isNotificationResult(value: unknown): value is NotificationResult {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  const result = value as Record<string, unknown>;

  return (
    typeof result.id === 'string' &&
    typeof result.timestamp === 'number' &&
    isNotificationType(result.type) &&
    isNotificationChannel(result.channel) &&
    typeof result.dismissed === 'boolean' &&
    (result.dismissedAt === undefined || typeof result.dismissedAt === 'number') &&
    (result.actionClicked === undefined || typeof result.actionClicked === 'string') &&
    (result.error === undefined || result.error instanceof Error)
  );
}

/**
 * @description Utility Functions - Yardımcı fonksiyonlar
 */

/**
 * @function getNotificationTypePriority
 * @description Bildirim türünün öncelik değerini döner
 * @param type - Bildirim türü
 * @returns Öncelik değeri (düşük değer = yüksek öncelik)
 */
export function getNotificationTypePriority(type: NotificationType): number {
  const priorities: Record<NotificationType, number> = {
    [NotificationType.ERROR]: 0,
    [NotificationType.WARNING]: 1,
    [NotificationType.LOADING]: 2,
    [NotificationType.INFO]: 3,
    [NotificationType.SUCCESS]: 4,
  };

  return priorities[type];
}

/**
 * @function getDefaultDurationForType
 * @description Bildirim türüne göre varsayılan süreyi döner
 * @param type - Bildirim türü
 * @returns Varsayılan süre
 */
export function getDefaultDurationForType(type: NotificationType): NotificationDuration {
  const durations: Record<NotificationType, NotificationDuration> = {
    [NotificationType.SUCCESS]: NotificationDuration.MEDIUM,
    [NotificationType.INFO]: NotificationDuration.MEDIUM,
    [NotificationType.WARNING]: NotificationDuration.LONG,
    [NotificationType.ERROR]: NotificationDuration.EXTRA_LONG,
    [NotificationType.LOADING]: NotificationDuration.PERSISTENT,
  };

  return durations[type];
}

/**
 * @function getDefaultPositionForChannel
 * @description Bildirim kanalına göre varsayılan pozisyonu döner
 * @param channel - Bildirim kanalı
 * @returns Varsayılan pozisyon
 */
export function getDefaultPositionForChannel(channel: NotificationChannel): NotificationPosition {
  const positions: Record<NotificationChannel, NotificationPosition> = {
    [NotificationChannel.TOAST]: NotificationPosition.TOP_RIGHT,
    [NotificationChannel.DIALOG]: NotificationPosition.CENTER,
    [NotificationChannel.BANNER]: NotificationPosition.TOP,
    [NotificationChannel.PUSH]: NotificationPosition.TOP_RIGHT,
    [NotificationChannel.INLINE]: NotificationPosition.TOP,
  };

  return positions[channel];
}

/**
 * @function createNotificationOptions
 * @description Yeni bir NotificationOptions oluşturur
 * @param type - Bildirim türü
 * @param channel - Bildirim kanalı
 * @param overrides - Özelleştirmeler (opsiyonel)
 * @returns Oluşturulan NotificationOptions
 */
export function createNotificationOptions(
  type: NotificationType,
  channel: NotificationChannel,
  overrides?: Partial<NotificationOptions>
): NotificationOptions {
  const defaultOptions: NotificationOptions = {
    type,
    channel,
    position: getDefaultPositionForChannel(channel),
    duration: getDefaultDurationForType(type),
    persistent: type === NotificationType.LOADING || type === NotificationType.ERROR,
    dismissible: true,
    multiline: false,
    progress: type === NotificationType.LOADING,
  };

  return { ...defaultOptions, ...overrides };
}

/**
 * @function createNotificationContent
 * @description Yeni bir NotificationContent oluşturur
 * @param message - Bildirim mesajı
 * @param overrides - Özelleştirmeler (opsiyonel)
 * @returns Oluşturulan NotificationContent
 */
export function createNotificationContent(
  message: string,
  overrides?: Partial<NotificationContent>
): NotificationContent {
  const defaultContent: NotificationContent = {
    message,
  };

  return { ...defaultContent, ...overrides };
}

/**
 * @function createNotificationAction
 * @description Yeni bir NotificationAction oluşturur
 * @param id - Aksiyon ID'si
 * @param label - Aksiyon etiketi
 * @param handler - Aksiyon işleyici
 * @param overrides - Özelleştirmeler (opsiyonel)
 * @returns Oluşturulan NotificationAction
 */
export function createNotificationAction(
  id: string,
  label: string,
  handler: () => void | Promise<void>,
  overrides?: Partial<NotificationAction>
): NotificationAction {
  const defaultAction: NotificationAction = {
    id,
    label,
    handler,
    closeOnClick: true,
    disabled: false,
  };

  return { ...defaultAction, ...overrides };
}

/**
 * @function mergeNotificationOptions
 * @description İki NotificationOptions nesnesini birleştirir
 * @param base - Temel seçenekler
 * @param overrides - Üzerine yazılacak seçenekler
 * @returns Birleştirilmiş seçenekler
 */
export function mergeNotificationOptions(
  base: NotificationOptions,
  overrides: Partial<NotificationOptions>
): NotificationOptions {
  return {
    ...base,
    ...overrides,
    context: overrides.context ? { ...base.context, ...overrides.context } : base.context,
    style: overrides.style ? { ...base.style, ...overrides.style } : base.style,
    classes: overrides.classes || base.classes,
  };
}

/**
 * @function getDefaultNotificationConfiguration
 * @description Varsayılan bildirim konfigürasyonunu döner
 * @returns Varsayılan konfigürasyon
 */
export function getDefaultNotificationConfiguration(): NotificationConfiguration {
  return {
    defaultChannel: NotificationChannel.TOAST,
    defaultPosition: NotificationPosition.TOP_RIGHT,
    defaultDuration: NotificationDuration.MEDIUM,
    maxNotifications: 5,
    enableSound: false,
    enableVibration: false,
    enableGrouping: true,
    enablePersistence: true,
    enableRichContent: true,
    enableActions: true,
    theme: 'auto',
    animations: {
      enter: 'fadeIn',
      leave: 'fadeOut',
      duration: 300,
    },
  };
}

/**
 * @function generateNotificationId
 * @description Benzersiz bildirim ID'si oluşturur
 * @param prefix - ID öneki (opsiyonel)
 * @returns Benzersiz ID
 */
export function generateNotificationId(prefix = 'notification'): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}-${timestamp}-${random}`;
}

/**
 * @function shouldShowNotification
 * @description Bildirim gösterilip gösterilmeyeceğini kontrol eder
 * @param type - Bildirim türü
 * @param config - Bildirim konfigürasyonu
 * @returns Gösterilip gösterilmeyeceği
 */
export function shouldShowNotification(
  type: NotificationType,
  config: NotificationConfiguration
): boolean {
  // Temel kontroller
  if (!config.enableActions && type === NotificationType.LOADING) {
    return false;
  }

  if (!config.enableRichContent && type === NotificationType.INFO) {
    return config.enableActions;
  }

  return true;
}

/**
 * @function formatNotificationDuration
 * @description Bildirim süresini okunabilir formata çevirir
 * @param duration - Süre (ms veya enum)
 * @returns Formatlanmış süre
 */
export function formatNotificationDuration(duration: NotificationDuration | number): string {
  if ((duration as NotificationDuration) === NotificationDuration.PERSISTENT) {
    return 'Kalıcı';
  }

  const ms = typeof duration === 'number' ? duration : duration;
  const seconds = Math.round(ms / 1000);

  if (seconds < 60) {
    return `${seconds} saniye`;
  }

  const minutes = Math.round(seconds / 60);
  return `${minutes} dakika`;
}
