/**
 * @fileoverview Veri yazma işlemleri arayüzü
 * @description Bu dosya veri yazma işlemleri için arayüz tanımlarını içerir.
 * SOLID prensiplerinden Interface Segregation Principle'a uygun olarak
 * sadece yazma işlemlerini tanımlar.
 *
 * Single Responsibility: Sadece veri yazma işlemleri
 * Open/Closed: Yeni yazma metodları eklenebilir
 * Liskov Substitution: Tüm implementasyonlar birbirinin yerini alabilir
 * Interface Segregation: Sadec<PERSON> yazma işlemleri, okuma işlemleri ayrı
 * Dependency Inversion: Soyut arayüz, concrete implementasyonlara bağımlı değil
 */

import type {
  ID,
  Result,
  ValidationResult,
} from '../../types';

import type { BaseModel } from './BaseModel';

/**
 * @interface CreateOptions
 * @description Oluşturma seçenekleri arayüzü
 */
export interface CreateOptions {
  /**
   * @property validate
   * @description Oluşturmadan önce doğrulama yapılsın mı
   */
  readonly validate?: boolean;

  /**
   * @property skipDuplicateCheck
   * @description Duplicate kontrolü atlansin mı
   */
  readonly skipDuplicateCheck?: boolean;

  /**
   * @property generateId
   * @description ID otomatik oluşturulsun mu
   */
  readonly generateId?: boolean;

  /**
   * @property setTimestamps
   * @description Timestamp'ler otomatik ayarlansın mı
   */
  readonly setTimestamps?: boolean;
}

/**
 * @interface UpdateOptions
 * @description Güncelleme seçenekleri arayüzü
 */
export interface UpdateOptions {
  /**
   * @property validate
   * @description Güncellemeden önce doğrulama yapılsın mı
   */
  readonly validate?: boolean;

  /**
   * @property partial
   * @description Kısmi güncelleme mi (sadece değişen alanlar)
   */
  readonly partial?: boolean;

  /**
   * @property optimisticLocking
   * @description Optimistic locking kullanılsın mı
   */
  readonly optimisticLocking?: boolean;

  /**
   * @property updateTimestamp
   * @description UpdatedAt timestamp'i güncellensin mi
   */
  readonly updateTimestamp?: boolean;

  /**
   * @property incrementVersion
   * @description Version numarası artırılsın mı
   */
  readonly incrementVersion?: boolean;
}

/**
 * @interface DeleteOptions
 * @description Silme seçenekleri arayüzü
 */
export interface DeleteOptions {
  /**
   * @property soft
   * @description Soft delete mi (status = DELETED)
   */
  readonly soft?: boolean;

  /**
   * @property cascade
   * @description İlişkili kayıtlar da silinsin mi
   */
  readonly cascade?: boolean;

  /**
   * @property force
   * @description Zorla silme (soft delete'i bypass et)
   */
  readonly force?: boolean;
}

/**
 * @interface BatchCreateOptions
 * @description Toplu oluşturma seçenekleri arayüzü
 */
export interface BatchCreateOptions extends CreateOptions {
  /**
   * @property batchSize
   * @description Batch boyutu
   */
  readonly batchSize?: number;

  /**
   * @property continueOnError
   * @description Hata durumunda devam edilsin mi
   */
  readonly continueOnError?: boolean;

  /**
   * @property returnCreated
   * @description Oluşturulan kayıtlar döndürülsün mü
   */
  readonly returnCreated?: boolean;
}

/**
 * @interface BatchUpdateOptions
 * @description Toplu güncelleme seçenekleri arayüzü
 * @template T - Güncellenecek model tipi
 */
export interface BatchUpdateOptions extends UpdateOptions {
  /**
   * @property batchSize
   * @description Batch boyutu
   */
  readonly batchSize?: number;

  /**
   * @property continueOnError
   * @description Hata durumunda devam edilsin mi
   */
  readonly continueOnError?: boolean;

  /**
   * @property returnUpdated
   * @description Güncellenen kayıtlar döndürülsün mü
   */
  readonly returnUpdated?: boolean;
}

/**
 * @interface BatchDeleteOptions
 * @description Toplu silme seçenekleri arayüzü
 */
export interface BatchDeleteOptions extends DeleteOptions {
  /**
   * @property batchSize
   * @description Batch boyutu
   */
  readonly batchSize?: number;

  /**
   * @property continueOnError
   * @description Hata durumunda devam edilsin mi
   */
  readonly continueOnError?: boolean;

  /**
   * @property returnDeleted
   * @description Silinen kayıt ID'leri döndürülsün mü
   */
  readonly returnDeleted?: boolean;
}

/**
 * @interface BatchResult
 * @description Toplu işlem sonucu arayüzü
 * @template T - İşlem yapılan model tipi
 */
export interface BatchResult<T extends BaseModel> {
  /**
   * @property success
   * @description Başarılı işlem sayısı
   */
  readonly success: number;

  /**
   * @property failed
   * @description Başarısız işlem sayısı
   */
  readonly failed: number;

  /**
   * @property total
   * @description Toplam işlem sayısı
   */
  readonly total: number;

  /**
   * @property errors
   * @description Hata listesi
   */
  readonly errors: readonly Error[];

  /**
   * @property results
   * @description İşlem sonuçları (opsiyonel)
   */
  readonly results?: readonly T[];
}

/**
 * @interface IWriteDataSource
 * @description Veri yazma işlemleri arayüzü
 * @template T - Veri modeli tipi (BaseModel'den türetilmiş olmalı)
 *
 * Bu arayüz Interface Segregation Principle'a uygun olarak
 * sadece yazma işlemlerini tanımlar. Okuma işlemleri IReadDataSource'ta tanımlıdır.
 */
export interface IWriteDataSource<T extends BaseModel> {
  /**
   * @method create
   * @description Yeni bir kayıt oluşturur
   * @param data - Oluşturulacak veri
   * @param options - Oluşturma seçenekleri
   * @returns Oluşturulan kayıt
   */
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt' | 'version'>, options?: CreateOptions): Promise<Result<T, Error>>;

  /**
   * @method createWithId
   * @description Belirtilen ID ile yeni bir kayıt oluşturur
   * @param id - Kayıt ID'si
   * @param data - Oluşturulacak veri
   * @param options - Oluşturma seçenekleri
   * @returns Oluşturulan kayıt
   */
  createWithId(id: ID, data: Omit<T, 'id' | 'createdAt' | 'updatedAt' | 'version'>, options?: CreateOptions): Promise<Result<T, Error>>;

  /**
   * @method update
   * @description Mevcut bir kaydı günceller
   * @param id - Kayıt ID'si
   * @param data - Güncellenecek veri
   * @param options - Güncelleme seçenekleri
   * @returns Güncellenen kayıt
   */
  update(id: ID, data: Partial<Omit<T, 'id' | 'createdAt'>>, options?: UpdateOptions): Promise<Result<T, Error>>;

  /**
   * @method updateByModel
   * @description Model instance'ı ile güncelleme yapar
   * @param model - Güncellenecek model
   * @param options - Güncelleme seçenekleri
   * @returns Güncellenen kayıt
   */
  updateByModel(model: T, options?: UpdateOptions): Promise<Result<T, Error>>;

  /**
   * @method upsert
   * @description Kayıt varsa günceller, yoksa oluşturur
   * @param data - Upsert edilecek veri
   * @param options - Upsert seçenekleri
   * @returns Upsert edilen kayıt
   */
  upsert(data: Omit<T, 'createdAt' | 'updatedAt' | 'version'>, options?: CreateOptions & UpdateOptions): Promise<Result<T, Error>>;

  /**
   * @method delete
   * @description Bir kaydı siler
   * @param id - Silinecek kayıt ID'si
   * @param options - Silme seçenekleri
   * @returns Silme başarılı mı
   */
  delete(id: ID, options?: DeleteOptions): Promise<Result<boolean, Error>>;

  /**
   * @method deleteByModel
   * @description Model instance'ı ile silme yapar
   * @param model - Silinecek model
   * @param options - Silme seçenekleri
   * @returns Silme başarılı mı
   */
  deleteByModel(model: T, options?: DeleteOptions): Promise<Result<boolean, Error>>;

  /**
   * @method restore
   * @description Soft delete edilmiş bir kaydı geri yükler
   * @param id - Geri yüklenecek kayıt ID'si
   * @returns Geri yüklenen kayıt
   */
  restore(id: ID): Promise<Result<T, Error>>;

  /**
   * @method batchCreate
   * @description Toplu kayıt oluşturur
   * @param data - Oluşturulacak veri listesi
   * @param options - Toplu oluşturma seçenekleri
   * @returns Toplu işlem sonucu
   */
  batchCreate(data: readonly Omit<T, 'id' | 'createdAt' | 'updatedAt' | 'version'>[], options?: BatchCreateOptions): Promise<Result<BatchResult<T>, Error>>;

  /**
   * @method batchUpdate
   * @description Toplu güncelleme yapar
   * @param updates - Güncelleme listesi (id ve data içeren)
   * @param options - Toplu güncelleme seçenekleri
   * @returns Toplu işlem sonucu
   */
  batchUpdate(updates: readonly { id: ID; data: Partial<Omit<T, 'id' | 'createdAt'>> }[], options?: BatchUpdateOptions): Promise<Result<BatchResult<T>, Error>>;

  /**
   * @method batchDelete
   * @description Toplu silme yapar
   * @param ids - Silinecek kayıt ID'leri
   * @param options - Toplu silme seçenekleri
   * @returns Toplu işlem sonucu
   */
  batchDelete(ids: readonly ID[], options?: BatchDeleteOptions): Promise<Result<BatchResult<T>, Error>>;

  /**
   * @method validate
   * @description Veriyi doğrular (kaydetmeden)
   * @param data - Doğrulanacak veri
   * @returns Doğrulama sonucu
   */
  validate(data: Partial<T>): Promise<ValidationResult<T>>;

  /**
   * @method transaction
   * @description Transaction içinde işlem yapar
   * @param callback - Transaction callback'i
   * @returns Transaction sonucu
   */
  transaction<R>(callback: (dataSource: IWriteDataSource<T>) => Promise<Result<R, Error>>): Promise<Result<R, Error>>;
}
