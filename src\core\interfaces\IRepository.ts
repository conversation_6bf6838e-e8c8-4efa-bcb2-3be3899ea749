/**
 * @fileoverview Repository pattern aray<PERSON>zü
 * @description Bu dosya Repository pattern için arayüz tanımlarını içerir.
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak
 * üst seviye modüllerin alt seviye modüllere bağımlılığını ortadan kaldırır.
 *
 * Single Responsibility: Repository pattern işlemlerini tanımlar
 * Open/Closed: Yeni repository metodları eklenebilir
 * Liskov Substitution: Tüm implementasyonlar birbirinin yerini alabilir
 * Interface Segregation: Domain-specific işlemler için özelleştirilmiş arayüz
 * Dependency Inversion: Domain katmanı infrastructure'a bağımlı değil
 */

import type {
  ID,
  Result,
  PaginationParams,
  PaginatedResult,
  SortParams,
  FilterCondition,
  ValidationResult,
} from '../../types';

import type { BaseModel } from './BaseModel';

/**
 * @interface RepositoryOptions
 * @description Repository seçenekleri arayüzü
 * @template T - Repository model tipi
 */
export interface RepositoryOptions<T extends BaseModel> {
  /**
   * @property caching
   * @description Önbellekleme aktif mi
   */
  readonly caching?: boolean;

  /**
   * @property cacheTtl
   * @description Önbellek yaşam süresi (saniye)
   */
  readonly cacheTtl?: number;

  /**
   * @property validation
   * @description Otomatik doğrulama aktif mi
   */
  readonly validation?: boolean;

  /**
   * @property auditLog
   * @description Audit log tutulsun mu
   */
  readonly auditLog?: boolean;

  /**
   * @property softDelete
   * @description Soft delete kullanılsın mı
   */
  readonly softDelete?: boolean;

  /**
   * @property optimisticLocking
   * @description Optimistic locking kullanılsın mı
   */
  readonly optimisticLocking?: boolean;

  /**
   * @property defaultSort
   * @description Varsayılan sıralama
   */
  readonly defaultSort?: SortParams<T>[];

  /**
   * @property defaultFilters
   * @description Varsayılan filtreler
   */
  readonly defaultFilters?: FilterCondition<T>[];
}

/**
 * @interface RepositoryStats
 * @description Repository istatistikleri
 */
export interface RepositoryStats {
  /**
   * @property totalRecords
   * @description Toplam kayıt sayısı
   */
  readonly totalRecords: number;

  /**
   * @property activeRecords
   * @description Aktif kayıt sayısı
   */
  readonly activeRecords: number;

  /**
   * @property deletedRecords
   * @description Silinmiş kayıt sayısı
   */
  readonly deletedRecords: number;

  /**
   * @property cacheHitRate
   * @description Önbellek isabet oranı
   */
  readonly cacheHitRate?: number;

  /**
   * @property averageResponseTime
   * @description Ortalama yanıt süresi (ms)
   */
  readonly averageResponseTime?: number;

  /**
   * @property lastUpdated
   * @description Son güncelleme zamanı
   */
  readonly lastUpdated: Date;
}

/**
 * @interface DomainEvent
 * @description Domain event arayüzü
 * @template T - Event data tipi
 */
export interface DomainEvent<T = unknown> {
  /**
   * @property id
   * @description Event ID'si
   */
  readonly id: ID;

  /**
   * @property type
   * @description Event tipi
   */
  readonly type: string;

  /**
   * @property aggregateId
   * @description Aggregate ID'si
   */
  readonly aggregateId: ID;

  /**
   * @property aggregateType
   * @description Aggregate tipi
   */
  readonly aggregateType: string;

  /**
   * @property data
   * @description Event verisi
   */
  readonly data: T;

  /**
   * @property version
   * @description Event versiyonu
   */
  readonly version: number;

  /**
   * @property timestamp
   * @description Event zamanı
   */
  readonly timestamp: Date;

  /**
   * @property userId
   * @description İşlemi yapan kullanıcı ID'si
   */
  readonly userId?: ID;

  /**
   * @property metadata
   * @description Ek metadata
   */
  readonly metadata?: Record<string, unknown>;
}

/**
 * @interface IRepository
 * @description Repository pattern arayüzü
 * @template T - Domain model tipi (BaseModel'den türetilmiş olmalı)
 *
 * Bu arayüz Domain Driven Design'ın Repository pattern'ini implement eder.
 * Dependency Inversion Principle'a uygun olarak domain katmanının
 * infrastructure katmanına bağımlılığını ortadan kaldırır.
 */
export interface IRepository<T extends BaseModel> {
  /**
   * @method findById
   * @description ID'ye göre domain entity getirir
   * @param id - Entity ID'si
   * @returns Domain entity veya null
   */
  findById(id: ID): Promise<Result<T | null, Error>>;

  /**
   * @method findByIds
   * @description Birden fazla ID'ye göre domain entity'leri getirir
   * @param ids - Entity ID'leri
   * @returns Domain entity listesi
   */
  findByIds(ids: readonly ID[]): Promise<Result<readonly T[], Error>>;

  /**
   * @method findAll
   * @description Tüm domain entity'leri getirir
   * @param options - Sorgu seçenekleri
   * @returns Domain entity listesi
   */
  findAll(options?: {
    sort?: SortParams<T>[];
    filters?: FilterCondition<T>[];
  }): Promise<Result<readonly T[], Error>>;

  /**
   * @method findPaginated
   * @description Sayfalanmış domain entity'leri getirir
   * @param pagination - Sayfalama parametreleri
   * @param options - Sorgu seçenekleri
   * @returns Sayfalanmış domain entity'ler
   */
  findPaginated(
    pagination: PaginationParams,
    options?: {
      sort?: SortParams<T>[];
      filters?: FilterCondition<T>[];
    }
  ): Promise<Result<PaginatedResult<T>, Error>>;

  /**
   * @method findBySpecification
   * @description Specification pattern ile domain entity'leri getirir
   * @param specification - Domain specification
   * @returns Domain entity listesi
   */
  findBySpecification(specification: (entity: T) => boolean): Promise<Result<readonly T[], Error>>;

  /**
   * @method save
   * @description Domain entity'yi kaydeder (create veya update)
   * @param entity - Domain entity
   * @returns Kaydedilen domain entity
   */
  save(entity: T): Promise<Result<T, Error>>;

  /**
   * @method saveAll
   * @description Birden fazla domain entity'yi kaydeder
   * @param entities - Domain entity'ler
   * @returns Kaydedilen domain entity'ler
   */
  saveAll(entities: readonly T[]): Promise<Result<readonly T[], Error>>;

  /**
   * @method delete
   * @description Domain entity'yi siler
   * @param entity - Silinecek domain entity
   * @returns Silme başarılı mı
   */
  delete(entity: T): Promise<Result<boolean, Error>>;

  /**
   * @method deleteById
   * @description ID'ye göre domain entity'yi siler
   * @param id - Silinecek entity ID'si
   * @returns Silme başarılı mı
   */
  deleteById(id: ID): Promise<Result<boolean, Error>>;

  /**
   * @method deleteAll
   * @description Birden fazla domain entity'yi siler
   * @param entities - Silinecek domain entity'ler
   * @returns Silme başarılı mı
   */
  deleteAll(entities: readonly T[]): Promise<Result<boolean, Error>>;

  /**
   * @method exists
   * @description Domain entity'nin varlığını kontrol eder
   * @param id - Entity ID'si
   * @returns Entity var mı
   */
  exists(id: ID): Promise<Result<boolean, Error>>;

  /**
   * @method count
   * @description Domain entity sayısını döndürür
   * @param filters - Filtreleme koşulları
   * @returns Entity sayısı
   */
  count(filters?: FilterCondition<T>[]): Promise<Result<number, Error>>;

  /**
   * @method validate
   * @description Domain entity'yi doğrular
   * @param entity - Doğrulanacak domain entity
   * @returns Doğrulama sonucu
   */
  validate(entity: T): Promise<ValidationResult<T>>;

  /**
   * @method getNextId
   * @description Sonraki ID'yi getirir
   * @returns Yeni ID
   */
  getNextId(): Promise<Result<ID, Error>>;

  /**
   * @method clear
   * @description Repository önbelleğini temizler
   * @returns Temizleme başarılı mı
   */
  clear(): Promise<Result<boolean, Error>>;

  /**
   * @method refresh
   * @description Domain entity'yi veri kaynağından yeniden yükler
   * @param entity - Yenilenecek domain entity
   * @returns Yenilenmiş domain entity
   */
  refresh(entity: T): Promise<Result<T, Error>>;

  /**
   * @method getStatistics
   * @description Repository istatistiklerini getirir
   * @returns Repository istatistikleri
   */
  getStatistics(): Promise<Result<RepositoryStats, Error>>;

  /**
   * @method publishEvent
   * @description Domain event yayınlar
   * @param event - Domain event
   * @returns Event yayınlama başarılı mı
   */
  publishEvent(event: DomainEvent): Promise<Result<boolean, Error>>;

  /**
   * @method getEvents
   * @description Aggregate için domain event'leri getirir
   * @param aggregateId - Aggregate ID'si
   * @returns Domain event'ler
   */
  getEvents(aggregateId: ID): Promise<Result<readonly DomainEvent[], Error>>;

  /**
   * @method transaction
   * @description Transaction içinde işlem yapar
   * @param callback - Transaction callback'i
   * @returns Transaction sonucu
   */
  transaction<R>(callback: (repository: IRepository<T>) => Promise<Result<R, Error>>): Promise<Result<R, Error>>;
}

/**
 * @description Type Guards - IRepository için tip koruma fonksiyonları
 */

/**
 * @function isRepositoryOptions
 * @description Bir değerin RepositoryOptions olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns RepositoryOptions olup olmadığı
 */
export function isRepositoryOptions<T extends BaseModel>(value: unknown): value is RepositoryOptions<T> {
  return (
    typeof value === 'object' &&
    value !== null &&
    (
      !('caching' in value) ||
      typeof (value as RepositoryOptions<T>).caching === 'boolean'
    ) &&
    (
      !('cacheTtl' in value) ||
      typeof (value as RepositoryOptions<T>).cacheTtl === 'number'
    ) &&
    (
      !('validation' in value) ||
      typeof (value as RepositoryOptions<T>).validation === 'boolean'
    )
  );
}

/**
 * @function isDomainEvent
 * @description Bir değerin DomainEvent olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns DomainEvent olup olmadığı
 */
export function isDomainEvent(value: unknown): value is DomainEvent {
  return (
    typeof value === 'object' &&
    value !== null &&
    'id' in value &&
    'type' in value &&
    'aggregateId' in value &&
    'aggregateType' in value &&
    'data' in value &&
    'version' in value &&
    'timestamp' in value &&
    typeof (value as DomainEvent).id === 'string' &&
    typeof (value as DomainEvent).type === 'string' &&
    typeof (value as DomainEvent).aggregateId === 'string' &&
    typeof (value as DomainEvent).aggregateType === 'string' &&
    typeof (value as DomainEvent).version === 'number' &&
    (value as DomainEvent).timestamp instanceof Date
  );
}
