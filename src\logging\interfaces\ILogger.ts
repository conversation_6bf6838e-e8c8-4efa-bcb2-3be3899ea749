/**
 * @fileoverview Logger arayüzü ve ilgili tip tanımları
 * @description Bu dosya loglama sistemi için gerekli tüm interface'leri ve enum'ları içerir.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece loglama işlemleri ile ilgili arayüzleri tanımlar.
 *
 * Interface Segregation Principle'a uygun olarak küçük, özel amaçlı
 * arayüzler oluşturulmuştur. Dependency Inversion Principle'a uygun
 * olarak soyut arayüzler tanımlanmıştır.
 */

import type { Timestamp, Result } from '../../types';

/**
 * @enum LogLevel
 * @description Log seviyeleri enum'u
 * Open/Closed Principle'a uygun olarak yeni seviyeler eklenebilir
 * 
 * Seviyeler önem sırasına göre:
 * DEBUG < INFO < WARN < ERROR < FATAL
 */
export enum LogLevel {
  /**
   * @description Geliştirme aşamasında detaylı bilgi için kullanılır
   * Production ortamında genellikle kapatılır
   */
  DEBUG = 'debug',

  /**
   * @description Genel bilgilendirme mesajları için kullanılır
   * Normal işlem akışını gösterir
   */
  INFO = 'info',

  /**
   * @description Uyarı mesajları için kullanılır
   * Potansiyel problemleri gösterir ama uygulama çalışmaya devam eder
   */
  WARN = 'warn',

  /**
   * @description Hata mesajları için kullanılır
   * İşlem başarısız olmuştur ama uygulama çalışmaya devam edebilir
   */
  ERROR = 'error',

  /**
   * @description Kritik hatalar için kullanılır
   * Uygulama çalışamaz duruma gelmiştir
   */
  FATAL = 'fatal',
}

/**
 * @interface LogContext
 * @description Log mesajına ek bağlam bilgileri
 * Interface Segregation Principle'a uygun olarak
 * sadece bağlam bilgilerini içerir
 */
export interface LogContext {
  /**
   * @property userId
   * @description İşlemi yapan kullanıcının ID'si (varsa)
   */
  readonly userId?: string;

  /**
   * @property sessionId
   * @description Oturum ID'si (varsa)
   */
  readonly sessionId?: string;

  /**
   * @property requestId
   * @description HTTP request ID'si (varsa)
   */
  readonly requestId?: string;

  /**
   * @property component
   * @description Log'u üreten bileşen adı
   */
  readonly component?: string;

  /**
   * @property method
   * @description Log'u üreten metod adı
   */
  readonly method?: string;

  /**
   * @property tags
   * @description Log'u kategorize etmek için etiketler
   */
  readonly tags?: readonly string[];

  /**
   * @property metadata
   * @description Ek meta veriler
   */
  readonly metadata?: Record<string, unknown>;
}

/**
 * @interface LogEntry
 * @description Tek bir log kaydının yapısı
 * Single Responsibility Principle'a uygun olarak
 * sadece log kaydı bilgilerini içerir
 */
export interface LogEntry {
  /**
   * @property timestamp
   * @description Log kaydının oluşturulma zamanı
   */
  readonly timestamp: Timestamp;

  /**
   * @property level
   * @description Log seviyesi
   */
  readonly level: LogLevel;

  /**
   * @property message
   * @description Log mesajı
   */
  readonly message: string;

  /**
   * @property context
   * @description Ek bağlam bilgileri (opsiyonel)
   */
  readonly context: LogContext | undefined;

  /**
   * @property error
   * @description Hata nesnesi (hata logları için)
   */
  readonly error: Error | undefined;

  /**
   * @property stack
   * @description Stack trace (hata logları için)
   */
  readonly stack: string | undefined;
}

/**
 * @interface LoggerConfiguration
 * @description Logger yapılandırma arayüzü
 * Single Responsibility Principle'a uygun olarak
 * sadece logger konfigürasyonu ile ilgili özellikleri içerir
 */
export interface LoggerConfiguration {
  /**
   * @property minLevel
   * @description Minimum log seviyesi
   * Bu seviyenin altındaki loglar gösterilmez
   */
  readonly minLevel: LogLevel;

  /**
   * @property enableConsole
   * @description Konsola log yazılıp yazılmayacağı
   */
  readonly enableConsole: boolean;

  /**
   * @property enableFile
   * @description Dosyaya log yazılıp yazılmayacağı
   */
  readonly enableFile: boolean;

  /**
   * @property enableRemote
   * @description Uzak sunucuya log gönderilip gönderilmeyeceği
   */
  readonly enableRemote: boolean;

  /**
   * @property dateFormat
   * @description Tarih formatı
   */
  readonly dateFormat: string;

  /**
   * @property includeStackTrace
   * @description Stack trace dahil edilip edilmeyeceği
   */
  readonly includeStackTrace: boolean;

  /**
   * @property maxLogSize
   * @description Maksimum log dosyası boyutu (byte)
   */
  readonly maxLogSize?: number;

  /**
   * @property logRotation
   * @description Log dosyası rotasyonu aktif mi
   */
  readonly logRotation?: boolean;
}

/**
 * @interface ILogFormatter
 * @description Log formatı arayüzü
 * Interface Segregation Principle'a uygun olarak
 * sadece formatlama işlemlerini içerir
 */
export interface ILogFormatter {
  /**
   * @method format
   * @description Log entry'sini string formatına çevirir
   * @param entry - Formatlanacak log entry
   * @returns Formatlanmış log string'i
   */
  format(entry: LogEntry): string;

  /**
   * @method formatError
   * @description Hata nesnesini string formatına çevirir
   * @param error - Formatlanacak hata
   * @returns Formatlanmış hata string'i
   */
  formatError(error: Error): string;
}

/**
 * @interface ILogWriter
 * @description Log yazma arayüzü
 * Interface Segregation Principle'a uygun olarak
 * sadece yazma işlemlerini içerir
 */
export interface ILogWriter {
  /**
   * @method write
   * @description Formatlanmış log string'ini hedef ortama yazar
   * @param formattedLog - Formatlanmış log string'i
   * @param level - Log seviyesi
   * @returns Yazma işleminin sonucu
   */
  write(formattedLog: string, level: LogLevel): Promise<Result<void, Error>>;

  /**
   * @method flush
   * @description Bekleyen tüm log'ları zorla yazar
   * @returns Flush işleminin sonucu
   */
  flush(): Promise<Result<void, Error>>;

  /**
   * @method close
   * @description Writer'ı kapatır ve kaynakları temizler
   * @returns Kapatma işleminin sonucu
   */
  close(): Promise<Result<void, Error>>;
}

/**
 * @interface ILogger
 * @description Ana logger arayüzü
 * Single Responsibility Principle'a uygun olarak
 * sadece loglama işlemlerini tanımlar
 *
 * Dependency Inversion Principle'a uygun olarak
 * soyut bir arayüzdür, concrete implementasyonlara bağımlı değildir
 */
export interface ILogger {
  /**
   * @method debug
   * @description Debug seviyesinde log yazar
   * @param message - Log mesajı
   * @param context - Ek bağlam bilgileri (opsiyonel)
   * @returns Log yazma işleminin sonucu
   */
  debug(message: string, context?: LogContext): Promise<Result<void, Error>>;

  /**
   * @method info
   * @description Info seviyesinde log yazar
   * @param message - Log mesajı
   * @param context - Ek bağlam bilgileri (opsiyonel)
   * @returns Log yazma işleminin sonucu
   */
  info(message: string, context?: LogContext): Promise<Result<void, Error>>;

  /**
   * @method warn
   * @description Warn seviyesinde log yazar
   * @param message - Log mesajı
   * @param context - Ek bağlam bilgileri (opsiyonel)
   * @returns Log yazma işleminin sonucu
   */
  warn(message: string, context?: LogContext): Promise<Result<void, Error>>;

  /**
   * @method error
   * @description Error seviyesinde log yazar
   * @param message - Log mesajı
   * @param error - Hata nesnesi (opsiyonel)
   * @param context - Ek bağlam bilgileri (opsiyonel)
   * @returns Log yazma işleminin sonucu
   */
  error(
    message: string,
    error?: Error,
    context?: LogContext
  ): Promise<Result<void, Error>>;

  /**
   * @method fatal
   * @description Fatal seviyesinde log yazar
   * @param message - Log mesajı
   * @param error - Hata nesnesi (opsiyonel)
   * @param context - Ek bağlam bilgileri (opsiyonel)
   * @returns Log yazma işleminin sonucu
   */
  fatal(
    message: string,
    error?: Error,
    context?: LogContext
  ): Promise<Result<void, Error>>;

  /**
   * @method log
   * @description Belirtilen seviyede log yazar
   * @param level - Log seviyesi
   * @param message - Log mesajı
   * @param error - Hata nesnesi (opsiyonel)
   * @param context - Ek bağlam bilgileri (opsiyonel)
   * @returns Log yazma işleminin sonucu
   */
  log(
    level: LogLevel,
    message: string,
    error?: Error,
    context?: LogContext
  ): Promise<Result<void, Error>>;

  /**
   * @method isLevelEnabled
   * @description Belirtilen log seviyesinin aktif olup olmadığını kontrol eder
   * @param level - Kontrol edilecek log seviyesi
   * @returns Seviyenin aktif olup olmadığı
   */
  isLevelEnabled(level: LogLevel): boolean;

  /**
   * @method getConfiguration
   * @description Mevcut logger konfigürasyonunu döner
   * @returns Logger konfigürasyonu
   */
  getConfiguration(): LoggerConfiguration;

  /**
   * @method updateConfiguration
   * @description Logger konfigürasyonunu günceller
   * @param config - Yeni konfigürasyon
   * @returns Güncelleme işleminin sonucu
   */
  updateConfiguration(
    config: Partial<LoggerConfiguration>
  ): Promise<Result<void, Error>>;

  /**
   * @method flush
   * @description Bekleyen tüm log'ları zorla yazar
   * @returns Flush işleminin sonucu
   */
  flush(): Promise<Result<void, Error>>;

  /**
   * @method close
   * @description Logger'ı kapatır ve kaynakları temizler
   * @returns Kapatma işleminin sonucu
   */
  close(): Promise<Result<void, Error>>;
}

/**
 * @description Type Guards - Tip koruma fonksiyonları
 * Bu fonksiyonlar runtime'da tip güvenliği sağlar
 */

/**
 * @function isLogLevel
 * @description Bir değerin LogLevel enum'u olup olmadığını kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns LogLevel olup olmadığı
 */
export function isLogLevel(value: unknown): value is LogLevel {
  return typeof value === 'string' && Object.values(LogLevel).includes(value as LogLevel);
}

/**
 * @function isLogEntry
 * @description Bir nesnenin LogEntry interface'ini implement edip etmediğini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns LogEntry olup olmadığı
 */
export function isLogEntry(value: unknown): value is LogEntry {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  const entry = value as Record<string, unknown>;

  return (
    typeof entry.timestamp === 'number' &&
    isLogLevel(entry.level) &&
    typeof entry.message === 'string'
  );
}

/**
 * @function isLogContext
 * @description Bir nesnenin LogContext interface'ini implement edip etmediğini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns LogContext olup olmadığı
 */
export function isLogContext(value: unknown): value is LogContext {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  const context = value as Record<string, unknown>;

  // Tüm özellikler opsiyonel olduğu için sadece tip kontrolü yapıyoruz
  return (
    (context.userId === undefined || typeof context.userId === 'string') &&
    (context.sessionId === undefined || typeof context.sessionId === 'string') &&
    (context.requestId === undefined || typeof context.requestId === 'string') &&
    (context.component === undefined || typeof context.component === 'string') &&
    (context.method === undefined || typeof context.method === 'string') &&
    (context.tags === undefined || Array.isArray(context.tags)) &&
    (context.metadata === undefined || typeof context.metadata === 'object')
  );
}

/**
 * @description Utility Functions - Yardımcı fonksiyonlar
 */

/**
 * @function getLogLevelPriority
 * @description Log seviyesinin öncelik değerini döner
 * @param level - Log seviyesi
 * @returns Öncelik değeri (düşük değer = yüksek öncelik)
 */
export function getLogLevelPriority(level: LogLevel): number {
  const priorities: Record<LogLevel, number> = {
    [LogLevel.DEBUG]: 0,
    [LogLevel.INFO]: 1,
    [LogLevel.WARN]: 2,
    [LogLevel.ERROR]: 3,
    [LogLevel.FATAL]: 4,
  };

  return priorities[level];
}

/**
 * @function shouldLog
 * @description Belirtilen seviyenin minimum seviyeye göre loglanıp loglanmayacağını kontrol eder
 * @param level - Kontrol edilecek log seviyesi
 * @param minLevel - Minimum log seviyesi
 * @returns Loglanıp loglanmayacağı
 */
export function shouldLog(level: LogLevel, minLevel: LogLevel): boolean {
  return getLogLevelPriority(level) >= getLogLevelPriority(minLevel);
}

/**
 * @function createLogEntry
 * @description Yeni bir LogEntry oluşturur
 * @param level - Log seviyesi
 * @param message - Log mesajı
 * @param error - Hata nesnesi (opsiyonel)
 * @param context - Bağlam bilgileri (opsiyonel)
 * @returns Oluşturulan LogEntry
 */
export function createLogEntry(
  level: LogLevel,
  message: string,
  error?: Error,
  context?: LogContext
): LogEntry {
  return {
    timestamp: Date.now() as Timestamp,
    level,
    message,
    context,
    error,
    stack: error?.stack,
  };
}

/**
 * @function getDefaultLoggerConfiguration
 * @description Varsayılan logger konfigürasyonunu döner
 * @returns Varsayılan LoggerConfiguration
 */
export function getDefaultLoggerConfiguration(): LoggerConfiguration {
  return {
    minLevel: LogLevel.INFO,
    enableConsole: true,
    enableFile: false,
    enableRemote: false,
    dateFormat: 'YYYY-MM-DD HH:mm:ss',
    includeStackTrace: true,
    maxLogSize: 10 * 1024 * 1024, // 10MB
    logRotation: true,
  };
}
