/**
 * @fileoverview Bildirim sistemi arayüzleri merkezi export dosyası
 * @description Bu dosya tüm bildirim sistemi arayüzlerini merkezi bir noktadan export eder.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece interface export işlemini gerçekleştirir.
 *
 * Barrel export pattern kullanılarak kod organizasyonu sağlanır.
 * Bu yaklaşım Open/Closed Principle'a uygun olarak yeni interface'ler
 * eklendiğinde mevcut import'ları bozmaz.
 */

/**
 * @description Notification Enums Export
 * Bildirim sistemi enum'ları
 */
export {
  NotificationType,
  NotificationChannel,
  NotificationPosition,
  NotificationDuration,
} from './INotifier';

/**
 * @description Notification Interfaces Export
 * Bildirim sistemi interface'leri
 */
export type {
  NotificationAction,
  NotificationContent,
  NotificationContext,
  NotificationOptions,
  NotificationConfiguration,
  NotificationResult,
  INotificationFormatter,
  INotificationRenderer,
  INotifier,
} from './INotifier';

/**
 * @description Type Guards Export
 * Tip koruma fonksiyonları
 */
export {
  isNotificationType,
  isNotificationChannel,
  isNotificationPosition,
  isNotificationDuration,
  isNotificationAction,
  isNotificationContent,
  isNotificationOptions,
  isNotificationResult,
} from './INotifier';

/**
 * @description Utility Functions Export
 * Yardımcı fonksiyonlar
 */
export {
  getNotificationTypePriority,
  getDefaultDurationForType,
  getDefaultPositionForChannel,
  createNotificationOptions,
  createNotificationContent,
  createNotificationAction,
  mergeNotificationOptions,
  getDefaultNotificationConfiguration,
  generateNotificationId,
  shouldShowNotification,
  formatNotificationDuration,
} from './INotifier';

/**
 * @description Notification Registry - Bildirim sistemi kayıt defteri
 * Factory pattern ve Registry pattern için tip tanımları
 * 
 * Bu yapı Dependency Inversion Principle'a uygun olarak
 * concrete implementasyonlara bağımlı olmadan interface'ler
 * üzerinden çalışmayı sağlar.
 */

/**
 * @type NotificationFactory
 * @description Bildirim factory fonksiyon tipi
 * Factory pattern için kullanılır
 */
export type NotificationFactory = (
  config?: Partial<NotificationConfiguration>
) => Promise<INotifier>;

/**
 * @type NotificationFormatterFactory
 * @description Bildirim formatter factory fonksiyon tipi
 * Factory pattern için kullanılır
 */
export type NotificationFormatterFactory = (
  config?: Partial<NotificationConfiguration>
) => INotificationFormatter;

/**
 * @type NotificationRendererFactory
 * @description Bildirim renderer factory fonksiyon tipi
 * Factory pattern için kullanılır
 */
export type NotificationRendererFactory = (
  channel: NotificationChannel,
  config?: Partial<NotificationConfiguration>
) => INotificationRenderer;

/**
 * @interface NotificationRegistry
 * @description Bildirim sistemi kayıt defteri arayüzü
 * Registry pattern için kullanılır
 * 
 * Single Responsibility Principle'a uygun olarak
 * sadece kayıt işlemlerini yönetir
 */
export interface NotificationRegistry {
  /**
   * @method registerNotifier
   * @description Notifier factory'sini kayıt eder
   * @param name - Factory adı
   * @param factory - Factory fonksiyonu
   */
  registerNotifier(name: string, factory: NotificationFactory): void;

  /**
   * @method registerFormatter
   * @description Formatter factory'sini kayıt eder
   * @param name - Factory adı
   * @param factory - Factory fonksiyonu
   */
  registerFormatter(name: string, factory: NotificationFormatterFactory): void;

  /**
   * @method registerRenderer
   * @description Renderer factory'sini kayıt eder
   * @param channel - Bildirim kanalı
   * @param factory - Factory fonksiyonu
   */
  registerRenderer(channel: NotificationChannel, factory: NotificationRendererFactory): void;

  /**
   * @method getNotifier
   * @description Kayıtlı notifier factory'sini getirir
   * @param name - Factory adı
   * @returns Factory fonksiyonu (varsa)
   */
  getNotifier(name: string): NotificationFactory | undefined;

  /**
   * @method getFormatter
   * @description Kayıtlı formatter factory'sini getirir
   * @param name - Factory adı
   * @returns Factory fonksiyonu (varsa)
   */
  getFormatter(name: string): NotificationFormatterFactory | undefined;

  /**
   * @method getRenderer
   * @description Kayıtlı renderer factory'sini getirir
   * @param channel - Bildirim kanalı
   * @returns Factory fonksiyonu (varsa)
   */
  getRenderer(channel: NotificationChannel): NotificationRendererFactory | undefined;

  /**
   * @method createNotifier
   * @description Notifier instance'ı oluşturur
   * @param name - Factory adı
   * @param config - Konfigürasyon (opsiyonel)
   * @returns Notifier instance'ı
   */
  createNotifier(
    name: string,
    config?: Partial<NotificationConfiguration>
  ): Promise<INotifier>;

  /**
   * @method createFormatter
   * @description Formatter instance'ı oluşturur
   * @param name - Factory adı
   * @param config - Konfigürasyon (opsiyonel)
   * @returns Formatter instance'ı
   */
  createFormatter(
    name: string,
    config?: Partial<NotificationConfiguration>
  ): INotificationFormatter;

  /**
   * @method createRenderer
   * @description Renderer instance'ı oluşturur
   * @param channel - Bildirim kanalı
   * @param config - Konfigürasyon (opsiyonel)
   * @returns Renderer instance'ı
   */
  createRenderer(
    channel: NotificationChannel,
    config?: Partial<NotificationConfiguration>
  ): INotificationRenderer;

  /**
   * @method clear
   * @description Tüm kayıtları temizler
   */
  clear(): void;

  /**
   * @method getRegisteredNotifiers
   * @description Kayıtlı notifier isimlerini getirir
   * @returns Kayıtlı notifier isimleri
   */
  getRegisteredNotifiers(): readonly string[];

  /**
   * @method getRegisteredFormatters
   * @description Kayıtlı formatter isimlerini getirir
   * @returns Kayıtlı formatter isimleri
   */
  getRegisteredFormatters(): readonly string[];

  /**
   * @method getRegisteredRenderers
   * @description Kayıtlı renderer kanallarını getirir
   * @returns Kayıtlı renderer kanalları
   */
  getRegisteredRenderers(): readonly NotificationChannel[];
}

/**
 * @description Environment Configuration Support
 * Environment değişkenlerinden konfigürasyon okuma desteği
 * 
 * Bu yapı loglama sisteminden öğrenilen best practice'leri uygular
 */

/**
 * @interface NotificationEnvironmentConfig
 * @description Environment'tan okunacak bildirim konfigürasyonu
 * Single Responsibility Principle'a uygun olarak
 * sadece environment konfigürasyonu ile ilgili özellikleri içerir
 */
export interface NotificationEnvironmentConfig {
  /**
   * @property NOTIFICATION_DEFAULT_CHANNEL
   * @description Varsayılan bildirim kanalı
   */
  readonly NOTIFICATION_DEFAULT_CHANNEL?: string;

  /**
   * @property NOTIFICATION_DEFAULT_POSITION
   * @description Varsayılan bildirim pozisyonu
   */
  readonly NOTIFICATION_DEFAULT_POSITION?: string;

  /**
   * @property NOTIFICATION_DEFAULT_DURATION
   * @description Varsayılan bildirim süresi
   */
  readonly NOTIFICATION_DEFAULT_DURATION?: string;

  /**
   * @property NOTIFICATION_MAX_NOTIFICATIONS
   * @description Maksimum eşzamanlı bildirim sayısı
   */
  readonly NOTIFICATION_MAX_NOTIFICATIONS?: string;

  /**
   * @property NOTIFICATION_ENABLE_SOUND
   * @description Ses efekti aktif mi
   */
  readonly NOTIFICATION_ENABLE_SOUND?: string;

  /**
   * @property NOTIFICATION_ENABLE_VIBRATION
   * @description Titreşim aktif mi
   */
  readonly NOTIFICATION_ENABLE_VIBRATION?: string;

  /**
   * @property NOTIFICATION_ENABLE_GROUPING
   * @description Bildirim gruplama aktif mi
   */
  readonly NOTIFICATION_ENABLE_GROUPING?: string;

  /**
   * @property NOTIFICATION_THEME
   * @description Bildirim teması
   */
  readonly NOTIFICATION_THEME?: string;
}

/**
 * @function parseNotificationEnvironmentConfig
 * @description Environment değişkenlerinden bildirim konfigürasyonu oluşturur
 * @param env - Environment değişkenleri
 * @returns Bildirim konfigürasyonu
 */
export function parseNotificationEnvironmentConfig(
  env: NotificationEnvironmentConfig
): Partial<NotificationConfiguration> {
  const config: Partial<NotificationConfiguration> = {};

  // Channel parsing
  if (env.NOTIFICATION_DEFAULT_CHANNEL && isNotificationChannel(env.NOTIFICATION_DEFAULT_CHANNEL)) {
    config.defaultChannel = env.NOTIFICATION_DEFAULT_CHANNEL;
  }

  // Position parsing
  if (env.NOTIFICATION_DEFAULT_POSITION && isNotificationPosition(env.NOTIFICATION_DEFAULT_POSITION)) {
    config.defaultPosition = env.NOTIFICATION_DEFAULT_POSITION;
  }

  // Duration parsing
  if (env.NOTIFICATION_DEFAULT_DURATION) {
    const duration = parseInt(env.NOTIFICATION_DEFAULT_DURATION, 10);
    if (!isNaN(duration) && isNotificationDuration(duration)) {
      config.defaultDuration = duration;
    }
  }

  // Max notifications parsing
  if (env.NOTIFICATION_MAX_NOTIFICATIONS) {
    const maxNotifications = parseInt(env.NOTIFICATION_MAX_NOTIFICATIONS, 10);
    if (!isNaN(maxNotifications) && maxNotifications > 0) {
      config.maxNotifications = maxNotifications;
    }
  }

  // Boolean flags parsing
  if (env.NOTIFICATION_ENABLE_SOUND !== undefined) {
    config.enableSound = env.NOTIFICATION_ENABLE_SOUND === 'true';
  }

  if (env.NOTIFICATION_ENABLE_VIBRATION !== undefined) {
    config.enableVibration = env.NOTIFICATION_ENABLE_VIBRATION === 'true';
  }

  if (env.NOTIFICATION_ENABLE_GROUPING !== undefined) {
    config.enableGrouping = env.NOTIFICATION_ENABLE_GROUPING === 'true';
  }

  // Theme parsing
  if (env.NOTIFICATION_THEME && ['light', 'dark', 'auto'].includes(env.NOTIFICATION_THEME)) {
    config.theme = env.NOTIFICATION_THEME as 'light' | 'dark' | 'auto';
  }

  return config;
}
